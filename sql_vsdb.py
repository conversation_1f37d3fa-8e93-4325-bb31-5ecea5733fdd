import sqlite3
import os

db_path = os.path.join(os.getenv('APPDATA'), 'Cursor', 'User', 'globalStorage', 'state.vscdb')

conn = None
try:
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查看表结构
    # cursor.execute("PRAGMA table_info(itemTable);")
    # cursor.execute("select key, value from itemTable where key='cursorAuth/cachedEmail'")
    # cursor.execute("select key, value from itemTable where key='cursorAuth/refreshToken'")
    cursor.execute("select key, value from itemTable where key='cursorAuth/accessToken'")
    cursor.execute("select key, value from itemTable ")
    
    columns = cursor.fetchall()
    
    print("itemTable 列信息:")
    for col in columns:
        print(col)
    # print(columns[0][1])
except sqlite3.Error as e:
    print("数据库错误:", str(e))
finally:
    if conn:
        conn.close()