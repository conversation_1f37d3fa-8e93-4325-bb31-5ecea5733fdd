(['C:\\Users\\<USER>\\Desktop\\cursor_project\\cursor_pro\\crazy_cursor_gui.py'],
 ['C:\\Users\\<USER>\\Desktop\\cursor_project\\cursor_pro'],
 ['customtkinter',
  'requests',
  'psutil',
  'logging',
  'json',
  'base64',
  'uuid',
  'hashlib',
  'sqlite3',
  'webbrowser',
  'queue',
  'threading',
  'winreg',
  'encodings.utf_8',
  'encodings.ascii',
  'encodings.idna',
  'charset_normalizer.md__mypyc',
  'PIL._tkinter_finder',
  'tkinter',
  '_tkinter',
  'PIL',
  'PIL._imaging',
  'PIL._tkinter_finder',
  'codecs'],
 ['C:\\Program '
  'Files\\Python37\\lib\\site-packages\\playwright\\_impl\\__pyinstaller',
  'C:\\Program Files\\Python37\\lib\\site-packages\\rapidfuzz\\__pyinstaller',
  'C:\\Program Files\\Python37\\lib\\site-packages\\tqsdk\\__pyinstaller',
  'C:\\Program '
  'Files\\Python37\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'C:\\Program '
  'Files\\Python37\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 ['matplotlib', 'pandas', 'scipy', 'notebook'],
 [],
 False,
 False,
 False,
 {},
 [],
 [('customtkinter\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\__init__.py',
   'DATA'),
  ('customtkinter\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\assets\\.DS_Store',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\.DS_Store',
   'DATA'),
  ('customtkinter\\assets\\fonts\\CustomTkinter_shapes_font.otf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\fonts\\CustomTkinter_shapes_font.otf',
   'DATA'),
  ('customtkinter\\assets\\fonts\\Roboto\\Roboto-Medium.ttf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\fonts\\Roboto\\Roboto-Medium.ttf',
   'DATA'),
  ('customtkinter\\assets\\fonts\\Roboto\\Roboto-Regular.ttf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\fonts\\Roboto\\Roboto-Regular.ttf',
   'DATA'),
  ('customtkinter\\assets\\icons\\.DS_Store',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\icons\\.DS_Store',
   'DATA'),
  ('customtkinter\\assets\\icons\\CustomTkinter_icon_Windows.ico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\icons\\CustomTkinter_icon_Windows.ico',
   'DATA'),
  ('customtkinter\\assets\\themes\\blue.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\themes\\blue.json',
   'DATA'),
  ('customtkinter\\assets\\themes\\dark-blue.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\themes\\dark-blue.json',
   'DATA'),
  ('customtkinter\\assets\\themes\\green.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\themes\\green.json',
   'DATA'),
  ('customtkinter\\windows\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\__pycache__\\ctk_input_dialog.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__pycache__\\ctk_input_dialog.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\__pycache__\\ctk_tk.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__pycache__\\ctk_tk.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\__pycache__\\ctk_toplevel.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__pycache__\\ctk_toplevel.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\ctk_input_dialog.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\ctk_input_dialog.py',
   'DATA'),
  ('customtkinter\\windows\\ctk_tk.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\ctk_tk.py',
   'DATA'),
  ('customtkinter\\windows\\ctk_toplevel.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\ctk_toplevel.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_button.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_button.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_checkbox.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_checkbox.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_combobox.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_combobox.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_entry.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_entry.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_frame.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_frame.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_label.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_label.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_optionmenu.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_optionmenu.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_progressbar.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_progressbar.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_radiobutton.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_radiobutton.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_scrollable_frame.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_scrollable_frame.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_scrollbar.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_scrollbar.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_segmented_button.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_segmented_button.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_slider.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_slider.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_switch.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_switch.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_tabview.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_tabview.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_textbox.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_textbox.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\appearance_mode_base_class.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\appearance_mode_base_class.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\appearance_mode_tracker.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\appearance_mode_tracker.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_base_class.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_base_class.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_tracker.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_tracker.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\ctk_canvas.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\ctk_canvas.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\draw_engine.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\draw_engine.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\ctk_canvas.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\ctk_canvas.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\draw_engine.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\draw_engine.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\ctk_base_class.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\ctk_base_class.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\dropdown_menu.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\dropdown_menu.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\ctk_base_class.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\ctk_base_class.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\dropdown_menu.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\dropdown_menu.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_button.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_button.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_checkbox.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_checkbox.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_combobox.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_combobox.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_entry.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_entry.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_frame.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_frame.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_label.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_label.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_optionmenu.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_optionmenu.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_progressbar.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_progressbar.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_radiobutton.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_radiobutton.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_scrollable_frame.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollable_frame.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_scrollbar.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollbar.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_segmented_button.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_segmented_button.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_slider.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_slider.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_switch.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_switch.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_tabview.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_tabview.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_textbox.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_textbox.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\__pycache__\\ctk_font.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\__pycache__\\ctk_font.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\__pycache__\\font_manager.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\__pycache__\\font_manager.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\ctk_font.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\ctk_font.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\font_manager.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\font_manager.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\image\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\image\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\image\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\image\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\image\\__pycache__\\ctk_image.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\image\\__pycache__\\ctk_image.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\image\\ctk_image.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\image\\ctk_image.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\__pycache__\\scaling_base_class.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__pycache__\\scaling_base_class.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\__pycache__\\scaling_tracker.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__pycache__\\scaling_tracker.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\scaling_base_class.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_base_class.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\scaling_tracker.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_tracker.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\theme\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\theme\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\theme\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\theme\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\theme\\__pycache__\\theme_manager.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\theme\\__pycache__\\theme_manager.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\theme\\theme_manager.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\theme\\theme_manager.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\utility\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\utility\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\utility\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\utility\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\utility\\__pycache__\\utility_functions.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\utility\\__pycache__\\utility_functions.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\utility\\utility_functions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\utility\\utility_functions.py',
   'DATA')],
 '3.7.5 (tags/v3.7.5:5c02a39a0b, Oct 15 2019, 00:11:34) [MSC v.1916 64 bit '
 '(AMD64)]',
 [('pyi_rth_pkgutil',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('crazy_cursor_gui',
   'C:\\Users\\<USER>\\Desktop\\cursor_project\\cursor_pro\\crazy_cursor_gui.py',
   'PYSOURCE')],
 [('pkg_resources',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('subprocess', 'C:\\Program Files\\Python37\\lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python37\\lib\\selectors.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python37\\lib\\signal.py', 'PYMODULE'),
  ('struct', 'C:\\Program Files\\Python37\\lib\\struct.py', 'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Program Files\\Python37\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python37\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email', 'C:\\Program Files\\Python37\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python37\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python37\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python37\\lib\\string.py', 'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python37\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python37\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python37\\lib\\quopri.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python37\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python37\\lib\\gettext.py', 'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python37\\lib\\copy.py', 'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python37\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python37\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python37\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python37\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python37\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python37\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python37\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python37\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python37\\lib\\argparse.py', 'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python37\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python37\\lib\\_strptime.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python37\\lib\\socket.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python37\\lib\\random.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python37\\lib\\bisect.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python37\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python37\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python37\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python37\\lib\\email\\generator.py',
   'PYMODULE'),
  ('uu', 'C:\\Program Files\\Python37\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python37\\lib\\optparse.py', 'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python37\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python37\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program Files\\Python37\\lib\\dataclasses.py',
   'PYMODULE'),
  ('contextlib', 'C:\\Program Files\\Python37\\lib\\contextlib.py', 'PYMODULE'),
  ('packaging._structures',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python37\\lib\\ast.py', 'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python37\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python37\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Program Files\\Python37\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('__future__', 'C:\\Program Files\\Python37\\lib\\__future__.py', 'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python37\\lib\\configparser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program Files\\Python37\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python37\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python37\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python37\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python37\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python37\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python37\\lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python37\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python37\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python37\\lib\\bz2.py', 'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('sysconfig', 'C:\\Program Files\\Python37\\lib\\sysconfig.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python37\\lib\\pprint.py', 'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python37\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python37\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('imp', 'C:\\Program Files\\Python37\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python37\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python37\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python37\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python37\\lib\\opcode.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python37\\lib\\textwrap.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python37\\lib\\tempfile.py', 'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python37\\lib\\email\\parser.py',
   'PYMODULE'),
  ('plistlib', 'C:\\Program Files\\Python37\\lib\\plistlib.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python37\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python37\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python37\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python37\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python37\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python37\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python37\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python37\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python37\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python37\\lib\\platform.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python37\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipfile', 'C:\\Program Files\\Python37\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'C:\\Program Files\\Python37\\lib\\py_compile.py', 'PYMODULE'),
  ('_distutils_hack',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Program Files\\Python37\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Program Files\\Python37\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Program Files\\Python37\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Program Files\\Python37\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Program Files\\Python37\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Program Files\\Python37\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Program Files\\Python37\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Program Files\\Python37\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Program Files\\Python37\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Program Files\\Python37\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Program Files\\Python37\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python37\\lib\\shlex.py', 'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\Program Files\\Python37\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\Program Files\\Python37\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Program Files\\Python37\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Program Files\\Python37\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Program Files\\Python37\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Program Files\\Python37\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Program Files\\Python37\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Program Files\\Python37\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Program Files\\Python37\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'C:\\Program Files\\Python37\\lib\\cgi.py', 'PYMODULE'),
  ('html', 'C:\\Program Files\\Python37\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'C:\\Program Files\\Python37\\lib\\html\\entities.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python37\\lib\\glob.py', 'PYMODULE'),
  ('setuptools._distutils.command.register',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python37\\lib\\getpass.py', 'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('site', 'C:\\Program Files\\Python37\\lib\\site.py', 'PYMODULE'),
  ('rlcompleter',
   'C:\\Program Files\\Python37\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('readline',
   'C:\\Program Files\\Python37\\lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('pyreadline.console',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline.console.console',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\console\\console.py',
   'PYMODULE'),
  ('pyreadline.console.event',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\console\\event.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python37\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Program Files\\Python37\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Program Files\\Python37\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Program Files\\Python37\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Program Files\\Python37\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Program Files\\Python37\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Program Files\\Python37\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pyreadline.console.ansi',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline.keysyms',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline.keysyms.keysyms',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline.keysyms.common',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline.keysyms.ironpython_keysyms',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline.keysyms.winconstants',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline.logger',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\logger.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Program Files\\Python37\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python37\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python37\\lib\\ssl.py', 'PYMODULE'),
  ('http', 'C:\\Program Files\\Python37\\lib\\http\\__init__.py', 'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Program Files\\Python37\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('smtplib', 'C:\\Program Files\\Python37\\lib\\smtplib.py', 'PYMODULE'),
  ('hmac', 'C:\\Program Files\\Python37\\lib\\hmac.py', 'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python37\\lib\\pickle.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python37\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('pyreadline.unicode_helper',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\unicode_helper.py',
   'PYMODULE'),
  ('pyreadline.py3k_compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline.console.ironpython_console',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\__init__.py',
   'PYMODULE'),
  ('pyreadline.release',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\release.py',
   'PYMODULE'),
  ('pyreadline.modes',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline.modes.vi',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.history',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.lineobj',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.wordmatcher',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline.modes.notemacs',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline.modes.emacs',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline.modes.basemode',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline.error',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\error.py',
   'PYMODULE'),
  ('pyreadline.lineeditor',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline.clipboard',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline.clipboard.win32_clipboard',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline.clipboard.no_clipboard',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline.clipboard.ironpython_clipboard',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline.rlmain',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\rlmain.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Program Files\\Python37\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', 'C:\\Program Files\\Python37\\lib\\pydoc.py', 'PYMODULE'),
  ('http.server',
   'C:\\Program Files\\Python37\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Program Files\\Python37\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python37\\lib\\mimetypes.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Program Files\\Python37\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Program Files\\Python37\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'C:\\Program Files\\Python37\\lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Program Files\\Python37\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Program Files\\Python37\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Program Files\\Python37\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'C:\\Program Files\\Python37\\lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Program Files\\Python37\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Program Files\\Python37\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'C:\\Program Files\\Python37\\lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('decimal', 'C:\\Program Files\\Python37\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Program Files\\Python37\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'C:\\Program Files\\Python37\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python37\\lib\\numbers.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Program Files\\Python37\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Program Files\\Python37\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Program Files\\Python37\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Program Files\\Python37\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Program Files\\Python37\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Program Files\\Python37\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Program Files\\Python37\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Program Files\\Python37\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Program Files\\Python37\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Program Files\\Python37\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Program Files\\Python37\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('difflib', 'C:\\Program Files\\Python37\\lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'C:\\Program Files\\Python37\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Program Files\\Python37\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python37\\lib\\csv.py', 'PYMODULE'),
  ('setuptools._vendor',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\Program Files\\Python37\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\Program Files\\Python37\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Program Files\\Python37\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Program Files\\Python37\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'C:\\Program Files\\Python37\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\Program Files\\Python37\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('importlib_metadata',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('zipp',
   'C:\\Program Files\\Python37\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.py310compat',
   'C:\\Program Files\\Python37\\lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\Program Files\\Python37\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Program Files\\Python37\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Program Files\\Python37\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Program Files\\Python37\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Program Files\\Python37\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('PIL',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\Program Files\\Python37\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('fractions', 'C:\\Program Files\\Python37\\lib\\fractions.py', 'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'C:\\Program Files\\Python37\\lib\\colorsys.py', 'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('olefile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\olefile\\__init__.py',
   'PYMODULE'),
  ('olefile.olefile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\olefile\\olefile.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('doctest', 'C:\\Program Files\\Python37\\lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'C:\\Program Files\\Python37\\lib\\pdb.py', 'PYMODULE'),
  ('code', 'C:\\Program Files\\Python37\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Program Files\\Python37\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'C:\\Program Files\\Python37\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'C:\\Program Files\\Python37\\lib\\cmd.py', 'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.machar',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._version',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Program Files\\Python37\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('defusedxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Program Files\\Python37\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Program Files\\Python37\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Program Files\\Python37\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Program Files\\Python37\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Program Files\\Python37\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Program Files\\Python37\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Program Files\\Python37\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Program Files\\Python37\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Program Files\\Python37\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Program Files\\Python37\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Program Files\\Python37\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('defusedxml.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Program Files\\Python37\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Program Files\\Python37\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Program Files\\Python37\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Program Files\\Python37\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Program Files\\Python37\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('PIL._tkinter_finder',
   'C:\\Program Files\\Python37\\lib\\site-packages\\PIL\\_tkinter_finder.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Program Files\\Python37\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Program Files\\Python37\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Program Files\\Python37\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python37\\lib\\hashlib.py', 'PYMODULE'),
  ('uuid', 'C:\\Program Files\\Python37\\lib\\uuid.py', 'PYMODULE'),
  ('netbios',
   'C:\\Program Files\\Python37\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python37\\lib\\base64.py', 'PYMODULE'),
  ('psutil',
   'C:\\Program Files\\Python37\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Program Files\\Python37\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\Program Files\\Python37\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\Program Files\\Python37\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Program Files\\Python37\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('requests',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Program Files\\Python37\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python37\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('simplejson',
   'C:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\__init__.py',
   'PYMODULE'),
  ('simplejson.scanner',
   'C:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\scanner.py',
   'PYMODULE'),
  ('simplejson.ordered_dict',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\simplejson\\ordered_dict.py',
   'PYMODULE'),
  ('simplejson.encoder',
   'C:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\encoder.py',
   'PYMODULE'),
  ('simplejson.compat',
   'C:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\compat.py',
   'PYMODULE'),
  ('simplejson.decoder',
   'C:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\decoder.py',
   'PYMODULE'),
  ('simplejson.raw_json',
   'C:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\raw_json.py',
   'PYMODULE'),
  ('simplejson.errors',
   'C:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\errors.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'C:\\Program Files\\Python37\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Program Files\\Python37\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Program Files\\Python37\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Program Files\\Python37\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Program Files\\Python37\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Program Files\\Python37\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.packages',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('ipaddress', 'C:\\Program Files\\Python37\\lib\\ipaddress.py', 'PYMODULE'),
  ('urllib3.util.queue',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3.request',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('socks',
   'C:\\Program Files\\Python37\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python37\\lib\\netrc.py', 'PYMODULE'),
  ('requests.certs',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Program Files\\Python37\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Program Files\\Python37\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x448',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.poly1305',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\poly1305.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hmac',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hashes',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed448',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed25519',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dsa',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dh',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.scrypt',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\scrypt.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Program Files\\Python37\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('chardet',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langcyrillicmodel',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\langcyrillicmodel.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.compat',
   'C:\\Program Files\\Python37\\lib\\site-packages\\chardet\\compat.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Program Files\\Python37\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'C:\\Program Files\\Python37\\lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Program Files\\Python37\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python37\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('nturl2path', 'C:\\Program Files\\Python37\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python37\\lib\\ftplib.py', 'PYMODULE'),
  ('stringprep', 'C:\\Program Files\\Python37\\lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python37\\lib\\_py_abc.py', 'PYMODULE'),
  ('json', 'C:\\Program Files\\Python37\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'C:\\Program Files\\Python37\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Program Files\\Python37\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program Files\\Python37\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('crazy_cursor_ctk',
   'C:\\Users\\<USER>\\Desktop\\cursor_project\\cursor_pro\\crazy_cursor_ctk.py',
   'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python37\\lib\\typing.py', 'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python37\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python37\\lib\\queue.py', 'PYMODULE'),
  ('webbrowser', 'C:\\Program Files\\Python37\\lib\\webbrowser.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python37\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python37\\lib\\_threading_local.py',
   'PYMODULE'),
  ('customtkinter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_tk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\ctk_tk.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility.utility_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\utility\\utility_functions.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.utility',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\utility\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\image\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.image.ctk_image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\image\\ctk_image.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollable_frame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollable_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_textbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_textbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_tabview',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_tabview.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_switch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_switch.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_slider',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_slider.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_segmented_button',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_segmented_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_scrollbar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_radiobutton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_radiobutton.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_progressbar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_progressbar.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_optionmenu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_optionmenu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_label',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_label.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_frame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_frame.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_entry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_entry.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_combobox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_combobox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_checkbox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_checkbox.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.ctk_button',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_button.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.ctk_base_class',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\ctk_base_class.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Program Files\\Python37\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_widget_classes.dropdown_menu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\dropdown_menu.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.draw_engine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\draw_engine.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.core_rendering.ctk_canvas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\ctk_canvas.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\theme\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.theme.theme_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\theme\\theme_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_tracker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_tracker.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.scaling.scaling_base_class',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.font_manager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\font_manager.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.font.ctk_font',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\ctk_font.py',
   'PYMODULE'),
  ('tkinter.font',
   'C:\\Program Files\\Python37\\lib\\tkinter\\font.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_tracker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_tracker.py',
   'PYMODULE'),
  ('darkdetect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\darkdetect\\__init__.py',
   'PYMODULE'),
  ('darkdetect._linux_detect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\darkdetect\\_linux_detect.py',
   'PYMODULE'),
  ('darkdetect._windows_detect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\darkdetect\\_windows_detect.py',
   'PYMODULE'),
  ('darkdetect._dummy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\darkdetect\\_dummy.py',
   'PYMODULE'),
  ('darkdetect._mac_detect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\darkdetect\\_mac_detect.py',
   'PYMODULE'),
  ('customtkinter.windows.widgets.appearance_mode.appearance_mode_base_class',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_base_class.py',
   'PYMODULE'),
  ('customtkinter.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__init__.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_input_dialog',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\ctk_input_dialog.py',
   'PYMODULE'),
  ('customtkinter.windows.ctk_toplevel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\ctk_toplevel.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'C:\\Program Files\\Python37\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'C:\\Program Files\\Python37\\lib\\tkinter\\dialog.py',
   'PYMODULE')],
 [('python37.dll', 'C:\\Program Files\\Python37\\python37.dll', 'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python37\\VCRUNTIME140.dll',
   'BINARY'),
  ('libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\.libs\\libopenblas.XWYDX2IKJW2NMTWSFYNGFUWKQU3LYTCZ.gfortran-win_amd64.dll',
   'BINARY'),
  ('select.pyd', 'C:\\Program Files\\Python37\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python37\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python37\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python37\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python37\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python37\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Program Files\\Python37\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python37\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\Program Files\\Python37\\lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python37\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python37\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python37\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python37\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\_imagingcms.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\_cffi_backend.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Program Files\\Python37\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\random\\mtrand.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\random\\_sfc64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\random\\_philox.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\random\\_pcg64.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\random\\_mt19937.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\random\\bit_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\random\\_generator.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\random\\_common.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Program Files\\Python37\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\Program Files\\Python37\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files\\Python37\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('win32\\win32wnet.pyd',
   'C:\\Program Files\\Python37\\lib\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\psutil\\_psutil_windows.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('simplejson\\_speedups.cp37-win_amd64.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\simplejson\\_speedups.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python37\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('pywin32_system32\\pywintypes37.dll',
   'C:\\Program '
   'Files\\Python37\\lib\\site-packages\\pywin32_system32\\pywintypes37.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Program Files\\Python37\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Program Files\\Python37\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('tcl86t.dll', 'C:\\Program Files\\Python37\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'C:\\Program Files\\Python37\\DLLs\\tk86t.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Program Files\\Python37\\DLLs\\sqlite3.dll', 'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python37\\python3.dll', 'BINARY')],
 [],
 [],
 [('customtkinter\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\__init__.py',
   'DATA'),
  ('customtkinter\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\assets\\.DS_Store',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\.DS_Store',
   'DATA'),
  ('customtkinter\\assets\\fonts\\CustomTkinter_shapes_font.otf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\fonts\\CustomTkinter_shapes_font.otf',
   'DATA'),
  ('customtkinter\\assets\\fonts\\Roboto\\Roboto-Medium.ttf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\fonts\\Roboto\\Roboto-Medium.ttf',
   'DATA'),
  ('customtkinter\\assets\\fonts\\Roboto\\Roboto-Regular.ttf',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\fonts\\Roboto\\Roboto-Regular.ttf',
   'DATA'),
  ('customtkinter\\assets\\icons\\.DS_Store',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\icons\\.DS_Store',
   'DATA'),
  ('customtkinter\\assets\\icons\\CustomTkinter_icon_Windows.ico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\icons\\CustomTkinter_icon_Windows.ico',
   'DATA'),
  ('customtkinter\\assets\\themes\\blue.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\themes\\blue.json',
   'DATA'),
  ('customtkinter\\assets\\themes\\dark-blue.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\themes\\dark-blue.json',
   'DATA'),
  ('customtkinter\\assets\\themes\\green.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\assets\\themes\\green.json',
   'DATA'),
  ('customtkinter\\windows\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\__pycache__\\ctk_input_dialog.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__pycache__\\ctk_input_dialog.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\__pycache__\\ctk_tk.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__pycache__\\ctk_tk.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\__pycache__\\ctk_toplevel.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\__pycache__\\ctk_toplevel.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\ctk_input_dialog.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\ctk_input_dialog.py',
   'DATA'),
  ('customtkinter\\windows\\ctk_tk.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\ctk_tk.py',
   'DATA'),
  ('customtkinter\\windows\\ctk_toplevel.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\ctk_toplevel.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_button.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_button.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_checkbox.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_checkbox.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_combobox.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_combobox.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_entry.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_entry.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_frame.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_frame.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_label.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_label.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_optionmenu.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_optionmenu.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_progressbar.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_progressbar.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_radiobutton.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_radiobutton.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_scrollable_frame.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_scrollable_frame.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_scrollbar.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_scrollbar.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_segmented_button.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_segmented_button.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_slider.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_slider.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_switch.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_switch.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_tabview.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_tabview.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\__pycache__\\ctk_textbox.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\__pycache__\\ctk_textbox.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\appearance_mode_base_class.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\appearance_mode_base_class.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\appearance_mode_tracker.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\__pycache__\\appearance_mode_tracker.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_base_class.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_base_class.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_tracker.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\appearance_mode\\appearance_mode_tracker.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\ctk_canvas.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\ctk_canvas.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\draw_engine.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\__pycache__\\draw_engine.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\ctk_canvas.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\ctk_canvas.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_rendering\\draw_engine.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_rendering\\draw_engine.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\ctk_base_class.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\ctk_base_class.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\dropdown_menu.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\__pycache__\\dropdown_menu.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\ctk_base_class.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\ctk_base_class.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\core_widget_classes\\dropdown_menu.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\core_widget_classes\\dropdown_menu.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_button.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_button.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_checkbox.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_checkbox.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_combobox.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_combobox.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_entry.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_entry.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_frame.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_frame.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_label.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_label.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_optionmenu.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_optionmenu.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_progressbar.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_progressbar.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_radiobutton.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_radiobutton.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_scrollable_frame.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollable_frame.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_scrollbar.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_scrollbar.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_segmented_button.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_segmented_button.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_slider.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_slider.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_switch.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_switch.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_tabview.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_tabview.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\ctk_textbox.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\ctk_textbox.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\__pycache__\\ctk_font.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\__pycache__\\ctk_font.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\__pycache__\\font_manager.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\__pycache__\\font_manager.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\ctk_font.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\ctk_font.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\font\\font_manager.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\font\\font_manager.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\image\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\image\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\image\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\image\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\image\\__pycache__\\ctk_image.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\image\\__pycache__\\ctk_image.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\image\\ctk_image.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\image\\ctk_image.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\__pycache__\\scaling_base_class.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__pycache__\\scaling_base_class.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\__pycache__\\scaling_tracker.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\__pycache__\\scaling_tracker.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\scaling_base_class.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_base_class.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\scaling\\scaling_tracker.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\scaling\\scaling_tracker.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\theme\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\theme\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\theme\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\theme\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\theme\\__pycache__\\theme_manager.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\theme\\__pycache__\\theme_manager.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\theme\\theme_manager.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\theme\\theme_manager.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\utility\\__init__.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\utility\\__init__.py',
   'DATA'),
  ('customtkinter\\windows\\widgets\\utility\\__pycache__\\__init__.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\utility\\__pycache__\\__init__.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\utility\\__pycache__\\utility_functions.cpython-37.pyc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\utility\\__pycache__\\utility_functions.cpython-37.pyc',
   'DATA'),
  ('customtkinter\\windows\\widgets\\utility\\utility_functions.py',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python37\\site-packages\\customtkinter\\windows\\widgets\\utility\\utility_functions.py',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\cursor_project\\cursor_pro\\build\\crazy_cursor\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tk\\icons.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tk\\focus.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\..\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\word.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\package.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\top_level.txt',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\top_level.txt',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\INSTALLER',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\INSTALLER',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tk\\obsolete.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tk\\license.terms',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tm.tcl', 'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tk\\choosedir.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\LICENSE',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\LICENSE',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('tk\\tk.tcl', 'C:\\Program Files\\Python37\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('cryptography-40.0.2.dist-info\\LICENSE.APACHE',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-40.0.2.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\parray.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tk\\palette.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\..\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tk\\megawidget.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\..\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tk\\listbox.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tk\\tclIndex', 'C:\\Program Files\\Python37\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('tk\\msgs\\cs.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('cryptography-40.0.2.dist-info\\WHEEL',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-40.0.2.dist-info\\WHEEL',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tk\\console.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\history.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('cryptography-40.0.2.dist-info\\top_level.txt',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-40.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tk\\clrpick.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tk\\scale.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tk\\dialog.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\METADATA',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\METADATA',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Program Files\\Python37\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\RECORD',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\RECORD',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tk\\safetk.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tk\\comdlg.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('cryptography-40.0.2.dist-info\\LICENSE.PSF',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-40.0.2.dist-info\\LICENSE.PSF',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tk\\msgbox.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tk\\optMenu.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tk\\images\\README',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tk\\iconlist.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\init.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\tclIndex',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('cryptography-40.0.2.dist-info\\LICENSE.BSD',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-40.0.2.dist-info\\LICENSE.BSD',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\auto.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\menu.tcl', 'C:\\Program Files\\Python37\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('cryptography-40.0.2.dist-info\\LICENSE',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-40.0.2.dist-info\\LICENSE',
   'DATA'),
  ('tcl\\clock.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('cryptography-40.0.2.dist-info\\RECORD',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-40.0.2.dist-info\\RECORD',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tk\\spinbox.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tk\\tearoff.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('importlib_metadata-4.13.0.dist-info\\WHEEL',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\importlib_metadata-4.13.0.dist-info\\WHEEL',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tk\\text.tcl', 'C:\\Program Files\\Python37\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('tk\\unsupported.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('cryptography-40.0.2.dist-info\\METADATA',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-40.0.2.dist-info\\METADATA',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Program Files\\Python37\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-40.0.2.dist-info\\INSTALLER',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\cryptography-40.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tk\\button.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tk\\bgerror.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Program '
   'Files\\Python37\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tk\\entry.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'C:\\Program Files\\Python37\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\safe.tcl',
   'C:\\Program Files\\Python37\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('wheel-0.38.4.dist-info\\METADATA',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.38.4.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.38.4.dist-info\\RECORD',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.38.4.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.38.4.dist-info\\WHEEL',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.38.4.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.38.4.dist-info\\entry_points.txt',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.38.4.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.38.4.dist-info\\top_level.txt',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.38.4.dist-info\\top_level.txt',
   'DATA'),
  ('wheel-0.38.4.dist-info\\REQUESTED',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.38.4.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.38.4.dist-info\\LICENSE.txt',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.38.4.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.38.4.dist-info\\INSTALLER',
   'c:\\program '
   'files\\python37\\lib\\site-packages\\wheel-0.38.4.dist-info\\INSTALLER',
   'DATA')],
 [])
