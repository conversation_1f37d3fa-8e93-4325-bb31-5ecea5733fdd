// ==UserScript==
// @name         Cursor Register Helper
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  自动填充邮箱和验证码，辅助 Cursor 注册
// <AUTHOR> Agent
// @match        *://*.cursor.sh/*
// @match        *://*.cursor.com/*
// @grant        GM_addStyle
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_cookie
// @grant        GM.cookie
// @connect      api.mail.cx
// @connect      api2.cursor.sh
// @connect      www.cursor.com
// ==/UserScript==

(function() {
    'use strict';

    // ==================== 样式设置 ====================
    GM_addStyle(`
        .register-helper {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            width: 300px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            font-family: Arial, sans-serif;
            font-size: 14px;
        }
        .register-helper-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .register-helper-title {
            font-weight: bold;
            color: #333;
        }
        .register-helper-close {
            cursor: pointer;
            color: #999;
            font-size: 18px;
        }
        .register-helper-close:hover {
            color: #333;
        }
        .register-helper-content {
            margin-bottom: 10px;
        }
        .register-helper-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .register-helper-button {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
            flex: 1 0 calc(50% - 8px);
            text-align: center;
        }
        .register-helper-button:hover {
            background: #3367d6;
        }
        #generate-email-btn {
            background: #4285f4;
            flex: 1 0 100%;
        }
        #fill-password-btn {
            background: #fd7e14;
        }
        #get-code-btn {
            background: #28a745;
        }
        #fill-code-btn {
            background: #17a2b8;
        }
        #clear-mailbox-btn {
            background: #ffc107;
            color: #212529;
        }
        #clear-data-btn {
            background: #dc3545;
        }
        #goto-settings-btn {
            background: #4caf50 !important;
            flex: 1 0 100%;
        }
        #detect-token-btn {
            background: #20c997;
        }
        .register-helper-status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            background: #f5f5f5;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
            color: #333;
            line-height: 1.4;
        }
        .register-helper-email {
            font-weight: bold;
            word-break: break-all;
            color: #2c3e50;
            background: #ecf0f1;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .register-helper-code {
            font-weight: bold;
            font-size: 16px;
            color: #e74c3c;
            background: #fdf2f2;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .register-helper-token {
            font-weight: bold;
            font-size: 12px;
            color: #27ae60;
            word-break: break-all;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ddd;
            margin-top: 5px;
        }
        .register-helper-error {
            color: #e74c3c !important;
            font-weight: bold;
        }
        .register-helper-success {
            color: #27ae60 !important;
            font-weight: bold;
        }
        .register-helper-warning {
            color: #f39c12 !important;
            font-weight: bold;
        }
    `);

    // ==================== 邮箱相关功能 ====================

    // MailCX错误类
    class MailCXError extends Error {
        constructor(message) {
            super(message);
            this.name = 'MailCXError';
        }
    }

    // MailCX API客户端
    class MailCX {
        constructor() {
            this.BASE_URL = 'https://api.mail.cx/api/v1';
            this.token = null;
        }

        async makeRequest(method, endpoint, options = {}) {
            const url = this.BASE_URL + endpoint;
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };

            if (this.token) {
                headers['Authorization'] = `Bearer ${this.token}`;
            }

            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: method,
                    url: url,
                    headers: headers,
                    data: options.body,
                    timeout: 30000,
                    onload: function(response) {
                        if (response.status >= 200 && response.status < 300) {
                            try {
                                const result = response.responseText ? JSON.parse(response.responseText) : {};
                                resolve(result);
                            } catch (e) {
                                resolve(response.responseText);
                            }
                        } else {
                            reject(new MailCXError(`请求失败: ${response.status} ${response.statusText}`));
                        }
                    },
                    onerror: function(error) {
                        reject(new MailCXError(`请求错误: ${error}`));
                    }
                });
            });
        }

        async authorize() {
            try {
                const response = await this.makeRequest('POST', '/auth/authorize_token', {
                    headers: {'Authorization': 'Bearer undefined'}
                });
                if (typeof response === 'string') {
                    this.token = response.replace(/"/g, '');
                } else {
                    throw new MailCXError('无法获取token');
                }
            } catch (e) {
                console.error('认证失败:', e);
                throw e;
            }
        }

        async getMailbox(email) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('GET', `/mailbox/${encodedEmail}`);
        }

        async getMessage(email, messageId) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('GET', `/mailbox/${encodedEmail}/${messageId}`);
        }

        async deleteMessage(email, messageId) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('DELETE', `/mailbox/${encodedEmail}/${messageId}`);
        }

        async deleteMailbox(email) {
            if (!this.token) {
                await this.authorize();
            }
            const encodedEmail = encodeURIComponent(email);
            return this.makeRequest('DELETE', `/mailbox/${encodedEmail}`);
        }
    }

    // 生成随机邮箱 - 完全按照cursor_reg_longtoken.py的逻辑
    function generateRandomEmail() {
        // 确保前缀长度至少为10位，最多15位
        const prefixLength = Math.floor(Math.random() * 6) + 10; // 10-15位
        const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let prefix = '';

        // 生成随机前缀
        for (let i = 0; i < prefixLength; i++) {
            prefix += characters.charAt(Math.floor(Math.random() * characters.length));
        }

        // 按照Python脚本的格式：prefix + 'uv' + '@uvhivmqp.me'
        return `${prefix}<EMAIL>`;
    }

    // 生成随机密码
    function generateRandomPassword(length = 12) {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let password = '';
        for (let i = 0; i < length; i++) {
            password += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return password;
    }

    // 从邮件内容中提取验证码 - 增强版
    function extractVerificationCode(emailContent) {
        if (!emailContent) return null;

        console.log('尝试从邮件内容提取验证码:', emailContent.substring(0, 200));

        // 多种验证码格式的正则表达式
        const codePatterns = [
            /Your verification code is:\s*(\d{6})/i,
            /verification code is:\s*(\d{6})/i,
            /code is:\s*(\d{6})/i,
            /code:\s*(\d{6})/i,
            /验证码[：:]\s*(\d{6})/i,
            /验证码为[：:]\s*(\d{6})/i,
            /(\d{6})\s*is your verification code/i,
            /(\d{6})\s*is your code/i,
            /use code\s*(\d{6})/i,
            /enter code\s*(\d{6})/i,
            /confirm with\s*(\d{6})/i
        ];

        // 尝试各种格式
        for (const pattern of codePatterns) {
            const match = emailContent.match(pattern);
            if (match && match[1]) {
                console.log('通过正则表达式提取到验证码:', match[1]);
                return match[1];
            }
        }

        // 如果正则表达式都没匹配到，尝试直接查找6位数字
        const numbers = emailContent.match(/\b\d{6}\b/g);
        if (numbers && numbers.length > 0) {
            // 过滤掉明显不是验证码的数字（如日期等）
            const validCodes = numbers.filter(num => {
                // 排除明显的日期格式
                return !emailContent.includes(`${num.substring(0,2)}/${num.substring(2,4)}/${num.substring(4,6)}`) &&
                       !emailContent.includes(`${num.substring(0,4)}-${num.substring(4,6)}`);
            });

            if (validCodes.length > 0) {
                console.log('通过6位数字匹配提取到验证码:', validCodes[validCodes.length - 1]);
                return validCodes[validCodes.length - 1]; // 取最后一个
            }
        }

        // 最后尝试查找4-8位数字
        const allNumbers = emailContent.match(/\b\d{4,8}\b/g);
        if (allNumbers && allNumbers.length > 0) {
            // 优先选择6位数字
            const sixDigits = allNumbers.filter(num => num.length === 6);
            if (sixDigits.length > 0) {
                console.log('通过4-8位数字匹配提取到验证码:', sixDigits[sixDigits.length - 1]);
                return sixDigits[sixDigits.length - 1];
            }
        }

        console.log('未能从邮件内容中提取到验证码');
        return null;
    }



    // ==================== 主要功能 ====================

    // 域名检测函数
    function getCurrentDomain() {
        return window.location.hostname.toLowerCase();
    }

    function isCursorShDomain() {
        const domain = getCurrentDomain();
        return domain.includes('cursor.sh') || domain.includes('authenticator.cursor.sh');
    }

    function isCursorComDomain() {
        const domain = getCurrentDomain();
        return domain.includes('cursor.com');
    }

    // 创建UI
    function createUI() {
        const container = document.createElement('div');
        container.className = 'register-helper';

        // 根据域名生成不同的按钮组合
        let buttonsHtml = '';
        let titleText = 'Cursor 助手';

        if (isCursorShDomain()) {
            // cursor.sh域名：显示注册相关功能
            titleText = 'Cursor 注册助手';
            buttonsHtml = `
                <button class="register-helper-button" id="generate-email-btn">生成并填充邮箱</button>
                <button class="register-helper-button" id="fill-password-btn">填充密码</button>
                <button class="register-helper-button" id="get-code-btn">获取验证码</button>
                <button class="register-helper-button" id="fill-code-btn">填充验证码</button>
                <button class="register-helper-button" id="clear-mailbox-btn">清空邮件</button>
                <button class="register-helper-button" id="clear-data-btn">清除本地数据</button>
            `;
        } else if (isCursorComDomain()) {
            // cursor.com域名：只显示Token检测功能
            titleText = 'Cursor Token助手';
            buttonsHtml = `
                <button class="register-helper-button" id="detect-token-btn">检测Token</button>
                <button class="register-helper-button" id="clear-data-btn">清除本地数据</button>
            `;
        } else {
            // 其他域名：显示提示信息
            titleText = 'Cursor 助手';
            buttonsHtml = `
                <div style="padding: 10px; text-align: center; color: #666;">
                    <p>请在以下页面使用此助手：</p>
                    <p><strong>cursor.sh</strong> - 注册功能</p>
                    <p><strong>cursor.com</strong> - Token获取</p>
                </div>
                <button class="register-helper-button" id="clear-data-btn">清除本地数据</button>
            `;
        }

        container.innerHTML = `
            <div class="register-helper-header">
                <div class="register-helper-title">${titleText}</div>
                <div class="register-helper-close">×</div>
            </div>
            <div class="register-helper-content">
                <div class="register-helper-actions">
                    ${buttonsHtml}
                </div>
                <div class="register-helper-status">
                    等待操作...
                </div>
            </div>
        `;
        document.body.appendChild(container);

        // 关闭按钮事件
        container.querySelector('.register-helper-close').addEventListener('click', () => {
            container.style.display = 'none';
        });

        return container;
    }

    // 保存和获取数据
    function saveData(key, value) {
        GM_setValue(key, value);
    }

    function getData(key, defaultValue = null) {
        return GM_getValue(key, defaultValue);
    }

    // 清除所有保存的数据
    function clearAllData() {
        saveData('currentEmail', '');
        saveData('verificationCode', '');
        saveData('password', '');
        saveData('token', '');
        saveData('longToken', '');
        console.log('已清除所有保存的数据');
    }

    // 检查输入框是否是验证码输入框
    function isCodeInput(input) {
        if (!input) return false;

        // 获取输入框的各种属性
        const placeholder = (input.getAttribute('placeholder') || '').toLowerCase();
        const name = (input.getAttribute('name') || '').toLowerCase();
        const id = (input.getAttribute('id') || '').toLowerCase();
        const ariaLabel = (input.getAttribute('aria-label') || '').toLowerCase();

        // 尝试获取关联的标签文本
        let labelText = '';
        const labelElement = document.querySelector(`label[for="${input.id}"]`);
        if (labelElement) {
            labelText = labelElement.textContent.toLowerCase();
        }

        // 检查是否包含验证码相关的关键词
        const codeKeywords = ['code', 'verification', 'verify', 'otp', '验证码', 'enter the code'];

        for (const keyword of codeKeywords) {
            if (
                placeholder.includes(keyword) ||
                name.includes(keyword) ||
                id.includes(keyword) ||
                ariaLabel.includes(keyword) ||
                labelText.includes(keyword) ||
                (input.parentElement && input.parentElement.textContent.toLowerCase().includes(keyword))
            ) {
                console.log(`输入框被识别为验证码输入框，匹配关键词: ${keyword}`, input);
                return true;
            }
        }

        // 检查输入框的最大长度，验证码输入框通常有长度限制
        const maxLength = input.getAttribute('maxlength');
        if (maxLength && parseInt(maxLength) <= 8) {
            console.log('输入框可能是验证码输入框(基于长度限制):', input);
            return true;
        }

        // 检查是否有数字键盘提示
        const inputMode = input.getAttribute('inputmode');
        if (inputMode === 'numeric' || inputMode === 'tel') {
            console.log('输入框可能是验证码输入框(基于输入模式):', input);
            return true;
        }

        // 检查是否有模式限制
        const pattern = input.getAttribute('pattern');
        if (pattern && (pattern.includes('\\d') || pattern.includes('[0-9]'))) {
            console.log('输入框可能是验证码输入框(基于模式限制):', input);
            return true;
        }

        return false;
    }

    // 查找邮箱输入框
    function findEmailInput() {
        console.log('开始查找邮箱输入框...');

        // 针对Cursor网站优化的选择器
        const selectors = [
            // 标准邮箱输入框
            'input[type="email"]',
            'input[name="email"]',
            'input[placeholder*="email" i]',
            'input[id*="email" i]',
            'input[aria-label*="email" i]',
            // Cursor特定选择器
            'input[placeholder*="Your email address" i]',
            'input[placeholder*="email address" i]',
            'input[name="address"]',
            'input[autocomplete="email"]',
            // 通用表单选择器
            'form input[type="text"]:first-of-type',
            'form input[type="email"]',
            'form input:not([type="password"]):not([type="hidden"]):not([type="submit"]):not([type="button"])',
            // 框架特定选择器
            '.MuiFormControl-root input',
            '.MuiInputBase-root input'
        ];

        for (const selector of selectors) {
            try {
                const input = document.querySelector(selector);
                if (input && !isCodeInput(input)) {
                    console.log(`找到邮箱输入框，使用选择器: ${selector}`);
                    return input;
                } else if (input) {
                    console.log(`选择器 ${selector} 找到的输入框被识别为验证码输入框，跳过`);
                }
            } catch (e) {
                console.log(`选择器 ${selector} 出错:`, e);
            }
        }

        console.log('未通过预定义选择器找到邮箱输入框，尝试分析所有输入框...');

        // 如果上面的选择器都没找到，尝试查找所有输入框，检查其属性
        const allInputs = document.querySelectorAll('input');
        console.log(`页面上共有 ${allInputs.length} 个输入框`);

        // 记录所有输入框的信息，方便调试
        allInputs.forEach((input, index) => {
            const placeholder = input.getAttribute('placeholder') || '';
            const name = input.getAttribute('name') || '';
            const id = input.getAttribute('id') || '';
            const type = input.getAttribute('type') || '';
            const required = input.hasAttribute('required');
            const classes = Array.from(input.classList).join(', ');

            console.log(`输入框 #${index}:`, {
                placeholder,
                name,
                id,
                type,
                required,
                classes,
                element: input
            });
        });

        // 尝试找到可能的邮箱输入框
        for (const input of allInputs) {
            if (isCodeInput(input)) {
                console.log('跳过验证码输入框:', input);
                continue;
            }

            const placeholder = input.getAttribute('placeholder') || '';
            const name = input.getAttribute('name') || '';
            const id = input.getAttribute('id') || '';
            const type = input.getAttribute('type') || '';

            if (
                placeholder.toLowerCase().includes('email') ||
                name.toLowerCase().includes('email') ||
                id.toLowerCase().includes('email') ||
                type === 'email' ||
                placeholder.toLowerCase().includes('address') ||
                name.toLowerCase().includes('address') ||
                id.toLowerCase().includes('address')
            ) {
                console.log('通过属性分析找到可能的邮箱输入框:', input);
                return input;
            }
        }

        // 如果还是找不到，尝试找到第一个非验证码的文本输入框
        const textInputs = Array.from(allInputs).filter(input =>
            (input.type === 'text' || input.type === 'email' || !input.type) && !isCodeInput(input)
        );

        if (textInputs.length > 0) {
            console.log('未找到明确的邮箱输入框，使用第一个非验证码的文本输入框:', textInputs[0]);
            return textInputs[0];
        }

        // 最后的尝试：使用第一个非验证码的输入框
        const nonCodeInputs = Array.from(allInputs).filter(input => !isCodeInput(input));
        if (nonCodeInputs.length > 0) {
            console.log('使用第一个非验证码的输入框作为邮箱输入框:', nonCodeInputs[0]);
            return nonCodeInputs[0];
        }

        console.log('未找到任何可用的邮箱输入框');
        return null;
    }

    // 查找验证码输入框
    function findCodeInput() {
        console.log('开始查找验证码输入框...');

        // 尝试多种选择器来找到验证码输入框
        const selectors = [
            'input[placeholder*="code" i]',
            'input[name*="code" i]',
            'input[aria-label*="code" i]',
            'input[id*="code" i]',
            'input[placeholder*="Enter the code" i]',
            'input[placeholder*="verification" i]',
            'input[placeholder*="验证码" i]',
            'form input[type="text"]',
            '.MuiFormControl-root input',
            '.MuiInputBase-root input'
        ];

        for (const selector of selectors) {
            try {
                const input = document.querySelector(selector);
                if (input) {
                    console.log(`找到验证码输入框，使用选择器: ${selector}`);
                    return input;
                }
            } catch (e) {
                console.log(`选择器 ${selector} 出错:`, e);
            }
        }

        console.log('未通过预定义选择器找到验证码输入框，尝试分析所有输入框...');

        // 如果上面的选择器都没找到，尝试查找所有输入框，检查其属性
        const allInputs = document.querySelectorAll('input');

        // 尝试找到可能的验证码输入框
        for (const input of allInputs) {
            const placeholder = input.getAttribute('placeholder') || '';
            const name = input.getAttribute('name') || '';
            const id = input.getAttribute('id') || '';

            if (
                placeholder.toLowerCase().includes('code') ||
                name.toLowerCase().includes('code') ||
                id.toLowerCase().includes('code') ||
                placeholder.toLowerCase().includes('验证码') ||
                name.toLowerCase().includes('验证码') ||
                id.toLowerCase().includes('验证码') ||
                placeholder.toLowerCase().includes('verification') ||
                name.toLowerCase().includes('verification') ||
                id.toLowerCase().includes('verification')
            ) {
                console.log('通过属性分析找到可能的验证码输入框:', input);
                return input;
            }
        }

        // 如果还是没找到，尝试找到类型为text的输入框（通常验证码输入框是text类型）
        const textInputs = Array.from(allInputs).filter(input => input.type === 'text');

        if (textInputs.length === 1) {
            // 如果只有一个text类型输入框，很可能是验证码输入框
            console.log('使用唯一的文本输入框作为验证码输入框:', textInputs[0]);
            return textInputs[0];
        } else if (textInputs.length > 1) {
            // 如果有多个text类型输入框，尝试找到最可能是验证码输入框的那个
            // 通常验证码输入框会有一些特征，比如长度限制、数字类型等
            for (const input of textInputs) {
                const maxLength = input.getAttribute('maxlength');
                if (maxLength && parseInt(maxLength) <= 8) {
                    // 验证码通常是6位数字，输入框长度限制通常不会超过8
                    console.log('找到可能的验证码输入框(基于长度限制):', input);
                    return input;
                }

                // 检查是否有数字键盘提示
                const inputMode = input.getAttribute('inputmode');
                if (inputMode === 'numeric' || inputMode === 'tel') {
                    console.log('找到可能的验证码输入框(基于输入模式):', input);
                    return input;
                }

                // 检查是否有模式限制
                const pattern = input.getAttribute('pattern');
                if (pattern && (pattern.includes('\\d') || pattern.includes('[0-9]'))) {
                    console.log('找到可能的验证码输入框(基于模式限制):', input);
                    return input;
                }
            }

            // 如果没有找到明确的验证码输入框，使用第二个文本输入框
            // 通常第一个是邮箱，第二个是验证码
            if (textInputs.length >= 2) {
                console.log('使用第二个文本输入框作为验证码输入框:', textInputs[1]);
                return textInputs[1];
            }

            // 如果以上都失败，使用最后一个文本输入框
            console.log('使用最后一个文本输入框作为验证码输入框:', textInputs[textInputs.length - 1]);
            return textInputs[textInputs.length - 1];
        }

        console.log('未找到任何可用的验证码输入框');
        return null;
    }

    // 查找密码输入框
    function findPasswordInput() {
        console.log('开始查找密码输入框...');

        // 针对Cursor网站优化的密码输入框选择器
        const selectors = [
            'input[type="password"]',
            'input[name="password"]',
            'input[placeholder*="password" i]',
            'input[placeholder*="Create a password" i]',
            'input[placeholder*="Enter password" i]',
            'input[id*="password" i]',
            'input[aria-label*="password" i]',
            'input[autocomplete="new-password"]',
            'input[autocomplete="current-password"]'
        ];

        for (const selector of selectors) {
            try {
                const input = document.querySelector(selector);
                if (input && input.type === 'password') {
                    console.log(`找到密码输入框，使用选择器: ${selector}`);
                    return input;
                }
            } catch (e) {
                console.log(`选择器 ${selector} 出错:`, e);
            }
        }

        // 如果没找到，查找所有password类型的输入框
        const passwordInputs = document.querySelectorAll('input[type="password"]');
        if (passwordInputs.length > 0) {
            console.log('找到密码输入框（通过类型查找）:', passwordInputs[0]);
            return passwordInputs[0];
        }

        console.log('未找到任何可用的密码输入框');
        return null;
    }

    // 智能填充输入框的通用方法
    function smartFillInput(value, inputType = 'email') {
        console.log(`尝试智能填充${inputType}:`, value);

        // 根据类型选择查找函数
        let findInputFunc;
        switch (inputType) {
            case 'email':
                findInputFunc = findEmailInput;
                break;
            case 'password':
                findInputFunc = findPasswordInput;
                break;
            case 'code':
                findInputFunc = findCodeInput;
                break;
            default:
                findInputFunc = findEmailInput;
        }

        // 尝试多种填充方法
        const fillMethods = [
            // 方法1: 使用专门的查找函数
            () => {
                const input = findInputFunc();
                if (input) {
                    return fillInputWithValue(input, value);
                }
                return false;
            },

            // 方法2: 通过标签文本查找
            () => {
                const keywords = inputType === 'email' ? ['email', '邮箱', 'address'] :
                               inputType === 'password' ? ['password', '密码'] :
                               ['code', '验证码', 'verification'];

                for (const keyword of keywords) {
                    const labels = Array.from(document.querySelectorAll('label')).filter(label =>
                        label.textContent.toLowerCase().includes(keyword.toLowerCase())
                    );

                    for (const label of labels) {
                        // 通过for属性查找
                        const forId = label.getAttribute('for');
                        if (forId) {
                            const input = document.getElementById(forId);
                            if (input && isValidInputForType(input, inputType)) {
                                return fillInputWithValue(input, value);
                            }
                        }

                        // 在标签内查找
                        const input = label.querySelector('input');
                        if (input && isValidInputForType(input, inputType)) {
                            return fillInputWithValue(input, value);
                        }

                        // 在标签后查找
                        let nextElement = label.nextElementSibling;
                        while (nextElement) {
                            const input = nextElement.querySelector('input');
                            if (input && isValidInputForType(input, inputType)) {
                                return fillInputWithValue(input, value);
                            }
                            nextElement = nextElement.nextElementSibling;
                        }
                    }
                }
                return false;
            },

            // 方法3: 通过表单查找
            () => {
                const forms = document.querySelectorAll('form');
                for (const form of forms) {
                    const inputs = Array.from(form.querySelectorAll('input')).filter(input =>
                        isValidInputForType(input, inputType)
                    );
                    if (inputs.length > 0) {
                        return fillInputWithValue(inputs[0], value);
                    }
                }
                return false;
            },

            // 方法4: 查找所有可见输入框
            () => {
                const allInputs = document.querySelectorAll('input');
                for (const input of allInputs) {
                    if (isValidInputForType(input, inputType) && isInputVisible(input)) {
                        return fillInputWithValue(input, value);
                    }
                }
                return false;
            }
        ];

        // 依次尝试各种方法
        for (let i = 0; i < fillMethods.length; i++) {
            try {
                console.log(`尝试填充方法 ${i + 1}`);
                if (fillMethods[i]()) {
                    console.log(`方法 ${i + 1} 填充成功`);
                    return true;
                }
            } catch (e) {
                console.error(`方法 ${i + 1} 出错:`, e);
            }
        }

        console.log(`所有填充方法都失败了`);
        return false;
    }

    // 检查输入框是否适合指定类型
    function isValidInputForType(input, inputType) {
        if (!input) return false;

        const placeholder = (input.getAttribute('placeholder') || '').toLowerCase();
        const name = (input.getAttribute('name') || '').toLowerCase();
        const id = (input.getAttribute('id') || '').toLowerCase();
        const type = input.type || '';

        switch (inputType) {
            case 'email':
                return (type === 'email' || type === 'text') &&
                       !isCodeInput(input) &&
                       (placeholder.includes('email') || name.includes('email') ||
                        id.includes('email') || placeholder.includes('address') ||
                        name.includes('address') || id.includes('address') ||
                        type === 'email');
            case 'password':
                return type === 'password';
            case 'code':
                return isCodeInput(input);
            default:
                return false;
        }
    }

    // 专门用于验证码的填充方法 - 模拟Python脚本的逐字符输入
    function fillVerificationCode(code) {
        console.log('开始填充验证码:', code);

        // 方法1: 查找验证码输入框并逐字符输入
        const codeInput = findCodeInput();
        if (codeInput) {
            console.log('找到验证码输入框，开始逐字符输入');
            return fillCodeCharByChar(codeInput, code);
        }

        // 方法2: 查找多个单字符输入框（分段验证码输入）
        const singleCharInputs = findSingleCharInputs();
        if (singleCharInputs.length >= code.length) {
            console.log('找到分段验证码输入框，开始分别填充');
            return fillSegmentedCode(singleCharInputs, code);
        }

        // 方法3: 查找所有可能的输入框
        const allInputs = document.querySelectorAll('input[type="text"], input[type="number"], input:not([type])');
        for (const input of allInputs) {
            if (isInputVisible(input) && !input.value) {
                console.log('尝试在空的可见输入框中填充验证码');
                if (fillCodeCharByChar(input, code)) {
                    return true;
                }
            }
        }

        console.log('所有验证码填充方法都失败了');
        return false;
    }

    // 查找单字符输入框（用于分段验证码）
    function findSingleCharInputs() {
        const inputs = Array.from(document.querySelectorAll('input'));
        return inputs.filter(input => {
            const maxLength = input.getAttribute('maxlength');
            return isInputVisible(input) &&
                   (maxLength === '1' ||
                    (input.style.width && parseInt(input.style.width) < 50) ||
                    input.classList.toString().includes('digit') ||
                    input.classList.toString().includes('char'));
        });
    }

    // 逐字符填充验证码（模拟Python脚本的方法）
    function fillCodeCharByChar(input, code) {
        try {
            console.log('开始逐字符填充验证码到输入框:', input);

            // 聚焦输入框
            input.focus();

            // 清空现有内容
            input.value = '';

            // 逐字符输入，模拟真实的用户输入
            for (let i = 0; i < code.length; i++) {
                const char = code[i];

                setTimeout(() => {
                    // 创建键盘事件
                    const keydownEvent = new KeyboardEvent('keydown', {
                        key: char,
                        code: `Digit${char}`,
                        keyCode: 48 + parseInt(char),
                        which: 48 + parseInt(char),
                        bubbles: true,
                        cancelable: true
                    });

                    const keypressEvent = new KeyboardEvent('keypress', {
                        key: char,
                        code: `Digit${char}`,
                        keyCode: 48 + parseInt(char),
                        which: 48 + parseInt(char),
                        bubbles: true,
                        cancelable: true
                    });

                    const keyupEvent = new KeyboardEvent('keyup', {
                        key: char,
                        code: `Digit${char}`,
                        keyCode: 48 + parseInt(char),
                        which: 48 + parseInt(char),
                        bubbles: true,
                        cancelable: true
                    });

                    // 触发键盘事件
                    input.dispatchEvent(keydownEvent);
                    input.dispatchEvent(keypressEvent);

                    // 更新输入框的值
                    input.value += char;

                    // 触发input事件
                    const inputEvent = new InputEvent('input', {
                        data: char,
                        inputType: 'insertText',
                        bubbles: true,
                        cancelable: true
                    });
                    input.dispatchEvent(inputEvent);
                    input.dispatchEvent(keyupEvent);

                    // 如果是最后一个字符，触发change和blur事件
                    if (i === code.length - 1) {
                        setTimeout(() => {
                            input.dispatchEvent(new Event('change', { bubbles: true }));
                            input.dispatchEvent(new Event('blur', { bubbles: true }));
                        }, 100);
                    }
                }, i * 100); // 每个字符间隔100ms
            }

            return true;
        } catch (e) {
            console.error('逐字符填充失败:', e);
            return false;
        }
    }

    // 填充分段验证码
    function fillSegmentedCode(inputs, code) {
        try {
            console.log('开始填充分段验证码');

            for (let i = 0; i < Math.min(inputs.length, code.length); i++) {
                const input = inputs[i];
                const char = code[i];

                setTimeout(() => {
                    input.focus();
                    input.value = char;

                    // 触发事件
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    input.dispatchEvent(new Event('change', { bubbles: true }));

                    // 自动跳转到下一个输入框
                    if (i < inputs.length - 1) {
                        inputs[i + 1].focus();
                    }
                }, i * 200); // 每个字符间隔200ms
            }

            return true;
        } catch (e) {
            console.error('分段验证码填充失败:', e);
            return false;
        }
    }

    // 检查输入框是否可见
    function isInputVisible(input) {
        return input.type !== 'hidden' &&
               !input.disabled &&
               !input.readOnly &&
               input.style.display !== 'none' &&
               input.style.visibility !== 'hidden' &&
               input.offsetParent !== null;
    }

    // 填充输入框并触发事件 - 简化版，只保留最有效的方法
    function fillInputWithValue(input, value) {
        if (!input) return false;

        try {
            console.log('填充输入框:', input, '值:', value);

            // 专门针对Cursor网站的填充方法
            input.focus();

            // 等待一小段时间确保聚焦完成
            setTimeout(() => {
                // 清空现有内容
                input.value = '';

                // 使用原生setter
                const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                nativeInputValueSetter.call(input, value);

                // 创建并触发input事件
                const inputEvent = new Event('input', {
                    bubbles: true,
                    cancelable: true,
                    composed: true
                });

                // 设置事件的target属性
                Object.defineProperty(inputEvent, 'target', {
                    writable: false,
                    value: input
                });

                input.dispatchEvent(inputEvent);

                // 触发change事件
                const changeEvent = new Event('change', {
                    bubbles: true,
                    cancelable: true
                });
                input.dispatchEvent(changeEvent);

                // 最后失去焦点
                input.blur();
            }, 100);

            return true;
        } catch (e) {
            console.error('填充输入框失败:', e);
            return false;
        }
    }

    // 主函数
    function init() {
        console.log('Cursor Register Helper 已加载');

        // 创建UI
        const ui = createUI();
        const statusDiv = ui.querySelector('.register-helper-status');

        // 创建邮件客户端
        const mailClient = new MailCX();

        // 从存储中获取当前使用的邮箱、密码和验证码
        let currentEmail = getData('currentEmail', '');
        let currentPassword = getData('password', '');
        let verificationCode = getData('verificationCode', '');

        // 如果有保存的邮箱和验证码，显示在状态栏中
        if (currentEmail) {
            statusDiv.innerHTML = `邮箱已生成: <div class="register-helper-email">${currentEmail}</div>`;

            if (currentPassword) {
                statusDiv.innerHTML += `<br>密码: <div class="register-helper-code">${currentPassword}</div>`;
            }

            if (verificationCode) {
                statusDiv.innerHTML += `<br>验证码: <div class="register-helper-code">${verificationCode}</div>`;
            }

            // 智能自动填充，只在cursor.sh域名下进行
            const smartAutoFill = () => {
                if (!isCursorShDomain()) {
                    console.log('不在cursor.sh域名下，跳过自动填充');
                    return;
                }

                const currentUrl = window.location.href;
                console.log('检查是否需要自动填充，当前URL:', currentUrl);

                // 只在注册页面自动填充邮箱
                if (currentUrl.includes('authenticator.cursor.sh/sign-up')) {
                    if (currentEmail && !currentUrl.includes('/password') && !currentUrl.includes('/verify')) {
                        console.log('检测到邮箱注册页面，尝试填充邮箱');
                        attemptEmailFill();
                    }
                }

                // 在验证码页面自动填充验证码
                if (currentUrl.includes('/verify') && verificationCode) {
                    console.log('检测到验证码页面，尝试填充验证码');
                    setTimeout(() => {
                        const codeFilled = fillVerificationCode(verificationCode);
                        if (codeFilled) {
                            console.log('自动填充验证码成功');
                        } else {
                            console.log('验证码填充失败');
                        }
                    }, 1000);
                }
            };

            // 邮箱填充尝试函数
            const attemptEmailFill = (attempt = 1, maxAttempts = 3) => {
                console.log(`尝试填充邮箱 (${attempt}/${maxAttempts})...`);

                // 检查页面是否有输入框
                const allInputs = document.querySelectorAll('input');
                if (allInputs.length === 0) {
                    console.log('页面还没有输入框，等待加载...');
                    if (attempt < maxAttempts) {
                        setTimeout(() => attemptEmailFill(attempt + 1, maxAttempts), 2000);
                    }
                    return;
                }

                const emailFilled = smartFillInput(currentEmail, 'email');
                if (emailFilled) {
                    console.log('自动填充邮箱成功');
                } else {
                    console.log(`第 ${attempt} 次邮箱填充失败`);
                    if (attempt < maxAttempts) {
                        setTimeout(() => attemptEmailFill(attempt + 1, maxAttempts), 2000);
                    } else {
                        statusDiv.innerHTML += '<br><span class="register-helper-error">自动填充邮箱失败，请手动复制</span>';
                    }
                }
            };

            // 延迟执行，确保页面加载完成
            setTimeout(() => smartAutoFill(), 2000);
        }

        // 生成并填充邮箱按钮
        const generateEmailBtn = ui.querySelector('#generate-email-btn');
        if (generateEmailBtn) {
            generateEmailBtn.addEventListener('click', async () => {
                if (!isCursorShDomain()) {
                    statusDiv.innerHTML = '<span class="register-helper-error">此功能只能在 cursor.sh 域名下使用</span>';
                    return;
                }

                try {
                    statusDiv.innerHTML = '<span class="register-helper-warning">正在清空转发邮箱并生成新邮箱...</span>';

                    // 先清空转发邮箱
                    try {
                        const forwardEmail = '<EMAIL>';
                        await mailClient.authorize();
                        await mailClient.deleteMailbox(forwardEmail);
                        console.log('转发邮箱已清空');
                    } catch (e) {
                        console.log('清空转发邮箱失败:', e);
                    }

                    statusDiv.innerHTML = '<span class="register-helper-warning">正在生成邮箱和密码...</span>';

                    // 生成随机邮箱和密码
                    currentEmail = generateRandomEmail();
                    currentPassword = generateRandomPassword();

                    console.log('生成的邮箱:', currentEmail);
                    console.log('生成的密码:', currentPassword);

                    // 保存到存储中
                    saveData('currentEmail', currentEmail);
                    saveData('password', currentPassword);

                    // 等待一下确保页面加载完成
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // 只尝试填充邮箱，不填充密码
                    const emailFilled = smartFillInput(currentEmail, 'email');

                    // 显示结果
                    let resultHtml = '';
                    if (emailFilled) {
                        resultHtml = `<span class="register-helper-success">邮箱已生成并填充:</span><br>邮箱: <div class="register-helper-email">${currentEmail}</div><br>密码: <div class="register-helper-code">${currentPassword}</div><br><span class="register-helper-warning">请在密码页面点击"填充密码"按钮</span>`;
                    } else {
                        resultHtml = `<span class="register-helper-warning">邮箱已生成，请手动复制:</span><br>邮箱: <div class="register-helper-email">${currentEmail}</div><br>密码: <div class="register-helper-code">${currentPassword}</div>`;
                    }

                    statusDiv.innerHTML = resultHtml;
                } catch (error) {
                    statusDiv.innerHTML = `<span class="register-helper-error">生成邮箱失败: ${error.message}</span>`;
                    console.error('生成邮箱失败:', error);
                }
            });
        }

        // 填充密码按钮
        const fillPasswordBtn = ui.querySelector('#fill-password-btn');
        if (fillPasswordBtn) {
            fillPasswordBtn.addEventListener('click', async () => {
                if (!isCursorShDomain()) {
                    statusDiv.innerHTML = '<span class="register-helper-error">此功能只能在 cursor.sh 域名下使用</span>';
                    return;
                }

                if (!currentPassword) {
                    statusDiv.innerHTML = '<span class="register-helper-error">请先生成邮箱和密码</span>';
                    return;
                }

                try {
                    statusDiv.innerHTML = '<span class="register-helper-warning">正在填充密码...</span>';

                    // 等待一下确保页面加载完成
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // 尝试填充密码
                    const passwordFilled = smartFillInput(currentPassword, 'password');

                    if (passwordFilled) {
                        statusDiv.innerHTML = `<span class="register-helper-success">密码已填充:</span> <div class="register-helper-code">${currentPassword}</div>`;
                    } else {
                        statusDiv.innerHTML = `<span class="register-helper-warning">密码:</span> <div class="register-helper-code">${currentPassword}</div><br><span class="register-helper-error">未找到密码输入框，请手动复制</span>`;
                    }
                } catch (error) {
                    statusDiv.innerHTML = `<span class="register-helper-error">填充密码失败: ${error.message}</span>`;
                    console.error('填充密码失败:', error);
                }
            });
        }

        // URL监听器 - 自动检测密码页面并填充（只在cursor.sh域名下）
        if (isCursorShDomain()) {
            let lastUrl = window.location.href;
            setInterval(() => {
                const currentUrl = window.location.href;
                if (currentUrl !== lastUrl) {
                    lastUrl = currentUrl;
                    console.log('URL变化:', currentUrl);

                    // 检测是否进入密码页面
                    if (currentUrl.startsWith('https://authenticator.cursor.sh/sign-up/password') && currentPassword) {
                        console.log('检测到密码页面，准备自动填充密码');

                        // 延迟一下等待页面加载
                        setTimeout(async () => {
                            statusDiv.innerHTML = '<span class="register-helper-warning">检测到密码页面，正在自动填充密码...</span>';

                            const passwordFilled = smartFillInput(currentPassword, 'password');

                            if (passwordFilled) {
                                statusDiv.innerHTML = `<span class="register-helper-success">已自动填充密码:</span> <div class="register-helper-code">${currentPassword}</div>`;
                            } else {
                                statusDiv.innerHTML = `<span class="register-helper-warning">自动填充失败，请点击"填充密码"按钮</span><br>密码: <div class="register-helper-code">${currentPassword}</div>`;
                            }
                        }, 1000);
                    }
                }
            }, 1000); // 每秒检查一次URL变化
        }

        // 获取验证码按钮
        const getCodeBtn = ui.querySelector('#get-code-btn');
        if (getCodeBtn) {
            getCodeBtn.addEventListener('click', async () => {
                if (!isCursorShDomain()) {
                    statusDiv.innerHTML = '<span class="register-helper-error">此功能只能在 cursor.sh 域名下使用</span>';
                    return;
                }

                if (!currentEmail) {
                    statusDiv.innerHTML = '<span class="register-helper-error">请先生成邮箱</span>';
                    return;
                }

                try {
                    statusDiv.innerHTML = '<span class="register-helper-warning">正在获取验证码邮件...</span>';

                    // 验证码转发邮箱 - 所有验证码都会转发到这个邮箱
                    const forwardEmail = '<EMAIL>';
                    console.log(`从转发邮箱获取验证码: ${forwardEmail}`);
                    console.log(`生成的邮箱: ${currentEmail}`);

                    // 等待邮件到达的函数
                    const waitForEmail = async (maxAttempts = 15, interval = 2000) => {
                        for (let attempt = 0; attempt < maxAttempts; attempt++) {
                            statusDiv.innerHTML = `<span class="register-helper-warning">正在检查转发邮箱 (${attempt + 1}/${maxAttempts})...</span>`;

                            try {
                                // 从转发邮箱获取邮件内容
                                const messages = await mailClient.getMailbox(forwardEmail);

                                if (Array.isArray(messages) && messages.length > 0) {
                                    console.log(`在转发邮箱中找到 ${messages.length} 封邮件`);

                                    // 查找最新的邮件（通常验证码邮件是最新的）
                                    for (let i = messages.length - 1; i >= 0; i--) {
                                        const msg = messages[i];
                                        console.log('检查转发邮件:', msg);

                                        // 检查是否是验证码邮件，并且是发送给我们生成的邮箱的
                                        const isVerificationEmail =
                                            (msg.from && (
                                                msg.from.toLowerCase().includes('cursor') ||
                                                msg.from.toLowerCase().includes('noreply') ||
                                                msg.from.toLowerCase().includes('auth')
                                            )) ||
                                            (msg.subject && (
                                                msg.subject.toLowerCase().includes('verification') ||
                                                msg.subject.toLowerCase().includes('code') ||
                                                msg.subject.toLowerCase().includes('verify')
                                            ));

                                        // 检查邮件内容是否包含我们生成的邮箱地址
                                        if (isVerificationEmail) {
                                            try {
                                                const detail = await mailClient.getMessage(forwardEmail, msg.id);
                                                const content = detail.body?.text || detail.body?.html || detail.text || '';

                                                // 检查邮件内容是否包含我们生成的邮箱
                                                if (content.includes(currentEmail)) {
                                                    console.log('找到对应邮箱的验证码邮件:', msg);
                                                    console.log('邮件内容:', content);

                                                    // 提取验证码
                                                    verificationCode = extractVerificationCode(content);

                                                    if (verificationCode) {
                                                        console.log('提取到验证码:', verificationCode);
                                                        return verificationCode;
                                                    }
                                                }
                                            } catch (e) {
                                                console.error('获取邮件详情失败:', e);
                                            }
                                        }
                                    }
                                }
                            } catch (e) {
                                console.error('检查转发邮箱时出错:', e);
                            }

                            // 等待一段时间后再次检查
                            await new Promise(resolve => setTimeout(resolve, interval));
                        }

                        throw new Error('未在转发邮箱中收到验证码邮件或无法提取验证码');
                    };

                    // 等待并获取验证码
                    verificationCode = await waitForEmail();

                    // 保存到存储中
                    saveData('verificationCode', verificationCode);

                    statusDiv.innerHTML = `<span class="register-helper-success">验证码已获取:</span> <div class="register-helper-code">${verificationCode}</div>`;
                } catch (error) {
                    statusDiv.innerHTML = `
                        <span class="register-helper-error">获取验证码失败: ${error.message}</span>
                        <div style="font-size: 11px; margin-top: 8px; padding: 6px; background: #fff3cd; border-radius: 3px; color: #856404;">
                            <strong>验证码获取说明：</strong><br>
                            1. 所有验证码都转发到 <EMAIL><br>
                            2. 通过 MailCX API 获取转发邮件<br>
                            3. 每2秒检查一次，最多30秒<br>
                            4. 请确保已在Cursor网站提交邮箱<br>
                            5. 脚本会自动匹配对应邮箱的验证码
                        </div>
                    `;
                    console.error('获取验证码失败:', error);
                }
            });
        }

        // 填充验证码按钮
        const fillCodeBtn = ui.querySelector('#fill-code-btn');
        if (fillCodeBtn) {
            fillCodeBtn.addEventListener('click', async () => {
                if (!isCursorShDomain()) {
                    statusDiv.innerHTML = '<span class="register-helper-error">此功能只能在 cursor.sh 域名下使用</span>';
                    return;
                }

                if (!verificationCode) {
                    statusDiv.innerHTML = '<span class="register-helper-error">请先获取验证码</span>';
                    return;
                }

                try {
                    statusDiv.innerHTML = '<span class="register-helper-warning">正在填充验证码...</span>';

                    // 等待一下确保页面加载完成
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // 使用专门的验证码填充方法
                    const codeFilled = fillVerificationCode(verificationCode);

                    if (codeFilled) {
                        statusDiv.innerHTML = `<span class="register-helper-success">验证码已填充:</span> <div class="register-helper-code">${verificationCode}</div><br><span class="register-helper-success">注册完成后可在cursor.com获取Token</span>`;
                    } else {
                        statusDiv.innerHTML = `<span class="register-helper-warning">验证码:</span> <div class="register-helper-code">${verificationCode}</div><br><span class="register-helper-error">未找到验证码输入框，请手动复制</span>`;
                    }
                } catch (error) {
                    statusDiv.innerHTML = `<span class="register-helper-error">填充验证码失败: ${error.message}</span>`;
                    console.error('填充验证码失败:', error);
                }
            });
        }

        // 清除本地数据按钮
        ui.querySelector('#clear-data-btn').addEventListener('click', () => {
            clearAllData();
            currentEmail = '';
            currentPassword = '';
            verificationCode = '';
            statusDiv.innerHTML = '<span class="register-helper-success">本地数据已清除</span><br><span style="font-size: 11px; color: #666;">已清除：邮箱、密码、验证码信息<br>邮箱服务器上的邮件未删除</span>';
        });



        // 清空邮件按钮
        const clearMailboxBtn = ui.querySelector('#clear-mailbox-btn');
        if (clearMailboxBtn) {
            clearMailboxBtn.addEventListener('click', async () => {
                if (!isCursorShDomain()) {
                    statusDiv.innerHTML = '<span class="register-helper-error">此功能只能在 cursor.sh 域名下使用</span>';
                    return;
                }

                try {
                    statusDiv.innerHTML = '<span class="register-helper-warning">正在清空邮件...</span>';

                    // 创建一个新的邮件客户端实例
                    const tempMailClient = new MailCX();
                    await tempMailClient.authorize();

                    // 清空转发邮箱（验证码接收邮箱）
                    const forwardEmail = '<EMAIL>';
                    let forwardCleared = false;
                    try {
                        await tempMailClient.deleteMailbox(forwardEmail);
                        console.log('转发邮箱已清空');
                        forwardCleared = true;
                    } catch (e) {
                        console.log('清空转发邮箱失败:', e);
                    }

                    // 如果有生成的邮箱，也清空它
                    let generatedCleared = false;
                    if (currentEmail) {
                        try {
                            await tempMailClient.deleteMailbox(currentEmail);
                            console.log('生成的邮箱已清空');
                            generatedCleared = true;
                        } catch (e) {
                            console.log('清空生成邮箱失败:', e);
                        }
                    }

                    // 显示结果
                    let resultHtml = '<span class="register-helper-success">邮件清空完成:</span><br>';
                    if (forwardCleared) {
                        resultHtml += '<span class="register-helper-success">✓ 转发邮箱 <EMAIL> 已清空</span><br>';
                    } else {
                        resultHtml += '<span class="register-helper-error">✗ 转发邮箱清空失败</span><br>';
                    }

                    if (currentEmail) {
                        if (generatedCleared) {
                            resultHtml += `<span class="register-helper-success">✓ 生成邮箱 ${currentEmail} 已清空</span><br>`;
                        } else {
                            resultHtml += `<span class="register-helper-error">✗ 生成邮箱清空失败</span><br>`;
                        }
                    }

                    resultHtml += '<span style="font-size: 11px; color: #666;">本地数据（邮箱、密码、验证码）未清除</span>';
                    statusDiv.innerHTML = resultHtml;

                } catch (error) {
                    statusDiv.innerHTML = `<span class="register-helper-error">清空邮件失败: ${error.message}</span>`;
                    console.error('清空邮件失败:', error);
                }
            });
        }

        // 检测Token按钮
        const detectTokenBtn = ui.querySelector('#detect-token-btn');
        if (detectTokenBtn) {
            detectTokenBtn.addEventListener('click', async () => {
                if (!isCursorComDomain()) {
                    statusDiv.innerHTML = '<span class="register-helper-error">此功能只能在 cursor.com 域名下使用</span>';
                    return;
                }

                try {
                    statusDiv.innerHTML = '<span class="register-helper-warning">正在检测Token...</span>';

                    const currentUrl = window.location.href;
                    console.log('当前URL:', currentUrl);

                    // 尝试从Cookie和页面中查找Token
                    const tokenFound = await detectTokenFromPage();

                    if (tokenFound) {
                        statusDiv.innerHTML = `<span class="register-helper-success">找到Token:</span><br><div class="register-helper-token">${tokenFound}</div><br><span class="register-helper-success">Token已自动保存</span>`;

                        // 保存Token
                        saveData('token', tokenFound);
                        console.log('Token已保存:', tokenFound);
                    } else {
                        // 显示手动获取指南
                        statusDiv.innerHTML = `
                            <span class="register-helper-warning">未自动检测到Token</span><br>
                            <div style="font-size: 11px; margin-top: 8px; padding: 6px; background: #e3f2fd; border-radius: 3px; color: #1565c0; border: 1px solid #bbdefb;">
                                <strong>方法1 - 开发者工具：</strong><br>
                                1. 按F12打开开发者工具<br>
                                2. 切换到"Application"或"应用程序"标签<br>
                                3. 左侧选择"Cookies" → "https://www.cursor.com"<br>
                                4. 找到"WorkosCursorSessionToken"<br>
                                5. 复制其Value值<br><br>
                                <strong>方法2 - 控制台命令：</strong><br>
                                1. 按F12打开开发者工具<br>
                                2. 切换到"Console"控制台标签<br>
                                3. 复制并执行以下代码：<br>
                                <code style="background: #f5f5f5; padding: 2px 4px; border-radius: 2px; font-family: monospace;">
                                document.cookie.split(';').find(c=>c.includes('WorkosCursorSessionToken'))?.split('=')[1]?.trim()
                                </code><br>
                                4. 复制输出的Token值
                            </div>
                            <button class="register-helper-button" id="manual-token-btn" style="margin-top: 8px; background: #2196f3;">手动输入Token</button>
                            <button class="register-helper-button" id="copy-console-cmd-btn" style="margin-top: 8px; background: #4caf50;">复制控制台命令</button>
                        `;

                        // 添加按钮事件
                        setTimeout(() => {
                            const manualTokenBtn = ui.querySelector('#manual-token-btn');
                            if (manualTokenBtn) {
                                manualTokenBtn.addEventListener('click', () => {
                                    const token = prompt('请粘贴WorkosCursorSessionToken的值:');
                                    if (token && token.trim()) {
                                        const trimmedToken = token.trim();
                                        saveData('token', trimmedToken);
                                        statusDiv.innerHTML = `<span class="register-helper-success">Token已手动保存:</span><br><div class="register-helper-token">${trimmedToken}</div>`;
                                        console.log('Token已手动保存:', trimmedToken);
                                    } else {
                                        statusDiv.innerHTML = '<span class="register-helper-error">未输入有效的Token</span>';
                                    }
                                });
                            }

                            const copyCmdBtn = ui.querySelector('#copy-console-cmd-btn');
                            if (copyCmdBtn) {
                                copyCmdBtn.addEventListener('click', () => {
                                    const command = "document.cookie.split(';').find(c=>c.includes('WorkosCursorSessionToken'))?.split('=')[1]?.trim()";
                                    navigator.clipboard.writeText(command).then(() => {
                                        statusDiv.innerHTML = `
                                            <span class="register-helper-success">控制台命令已复制到剪贴板！</span><br>
                                            <div style="font-size: 11px; margin-top: 8px; padding: 6px; background: #e8f5e8; border-radius: 3px; color: #2e7d32;">
                                                现在请：<br>
                                                1. 按F12打开开发者工具<br>
                                                2. 切换到"Console"控制台标签<br>
                                                3. 粘贴并按回车执行<br>
                                                4. 复制输出的Token值<br>
                                                5. 点击"手动输入Token"按钮粘贴
                                            </div>
                                            <button class="register-helper-button" id="manual-token-btn-2" style="margin-top: 8px; background: #2196f3;">手动输入Token</button>
                                        `;

                                        // 为新按钮添加事件
                                        setTimeout(() => {
                                            const manualTokenBtn2 = ui.querySelector('#manual-token-btn-2');
                                            if (manualTokenBtn2) {
                                                manualTokenBtn2.addEventListener('click', () => {
                                                    const token = prompt('请粘贴从控制台获取的Token值:');
                                                    if (token && token.trim()) {
                                                        const trimmedToken = token.trim().replace(/"/g, ''); // 移除可能的引号
                                                        saveData('token', trimmedToken);
                                                        statusDiv.innerHTML = `<span class="register-helper-success">Token已手动保存:</span><br><div class="register-helper-token">${trimmedToken}</div>`;
                                                        console.log('Token已手动保存:', trimmedToken);
                                                    } else {
                                                        statusDiv.innerHTML = '<span class="register-helper-error">未输入有效的Token</span>';
                                                    }
                                                });
                                            }
                                        }, 100);
                                    }).catch(() => {
                                        alert('复制失败，请手动复制以下命令：\n\ndocument.cookie.split(\';\').find(c=>c.includes(\'WorkosCursorSessionToken\'))?.split(\'=\')[1]?.trim()');
                                    });
                                });
                            }
                        }, 100);
                    }
                } catch (error) {
                    statusDiv.innerHTML = `<span class="register-helper-error">检测Token失败: ${error.message}</span>`;
                    console.error('检测Token失败:', error);
                }
            });
        }

        // 使用多种方法获取Cookie
        function getTokenFromCookie() {
            console.log('开始从Cookie中获取Token...');

            return new Promise((resolve) => {
                try {
                    // 方法1: 尝试直接读取所有Cookie并查找
                    console.log('方法1: 检查document.cookie');
                    const allCookies = document.cookie;
                    console.log('所有可见Cookie:', allCookies);

                    if (allCookies) {
                        const cookies = allCookies.split(';');
                        for (const cookie of cookies) {
                            const [name, value] = cookie.trim().split('=');
                            console.log('检查Cookie:', name, '=', value?.substring(0, 20) + '...');
                            if (name === 'WorkosCursorSessionToken' && value) {
                                console.log('通过document.cookie找到WorkosCursorSessionToken');
                                resolve(value);
                                return;
                            }
                        }
                    }

                    // 方法2: 尝试使用GM.cookie API (新版本)
                    if (typeof GM !== 'undefined' && GM.cookie) {
                        console.log('方法2: 尝试GM.cookie API');
                        GM.cookie.list({}).then(cookies => {
                            console.log('GM.cookie获取到的所有cookies:', cookies);
                            const targetCookie = cookies.find(c => c.name === 'WorkosCursorSessionToken');
                            if (targetCookie) {
                                console.log('通过GM.cookie找到WorkosCursorSessionToken:', targetCookie.value);
                                resolve(targetCookie.value);
                            } else {
                                console.log('GM.cookie未找到WorkosCursorSessionToken');
                                resolve(null);
                            }
                        }).catch(e => {
                            console.log('GM.cookie出错:', e);
                            resolve(null);
                        });
                    }
                    // 方法3: 尝试使用GM_cookie API (旧版本)
                    else if (typeof GM_cookie !== 'undefined') {
                        console.log('方法3: 尝试GM_cookie API');
                        GM_cookie.list({}, (cookies, error) => {
                            if (error) {
                                console.log('GM_cookie出错:', error);
                                resolve(null);
                            } else {
                                console.log('GM_cookie获取到的所有cookies:', cookies);
                                const targetCookie = cookies.find(c => c.name === 'WorkosCursorSessionToken');
                                if (targetCookie) {
                                    console.log('通过GM_cookie找到WorkosCursorSessionToken:', targetCookie.value);
                                    resolve(targetCookie.value);
                                } else {
                                    console.log('GM_cookie未找到WorkosCursorSessionToken');
                                    resolve(null);
                                }
                            }
                        });
                    } else {
                        console.log('GM_cookie API不可用');
                        resolve(null);
                    }
                } catch (e) {
                    console.error('获取Cookie时出错:', e);
                    resolve(null);
                }
            });
        }

        // Token检测函数 - 支持多种方法
        async function detectTokenFromPage() {
            console.log('开始检测Token...');

            // 1. 优先尝试从Cookie中获取WorkosCursorSessionToken
            try {
                const cookieToken = await getTokenFromCookie();
                if (cookieToken) {
                    console.log('从Cookie中获取到Token:', cookieToken);
                    return cookieToken;
                }
            } catch (e) {
                console.log('Cookie获取失败:', e);
            }

            // 2. 尝试从网络请求中拦截Token
            const networkToken = await interceptNetworkToken();
            if (networkToken) {
                console.log('从网络请求中获取到Token:', networkToken);
                return networkToken;
            }

            // 3. 尝试从页面元素中查找
            const pageToken = findTokenFromPageElements();
            if (pageToken) {
                console.log('从页面元素中获取到Token:', pageToken);
                return pageToken;
            }

            // 4. 尝试从localStorage/sessionStorage中查找
            const storageToken = findTokenFromStorage();
            if (storageToken) {
                console.log('从存储中获取到Token:', storageToken);
                return storageToken;
            }

            console.log('未找到Token');
            return null;
        }

        // 拦截网络请求获取Token
        function interceptNetworkToken() {
            return new Promise((resolve) => {
                let found = false;
                const originalFetch = window.fetch;
                const originalXHR = window.XMLHttpRequest.prototype.open;

                // 拦截fetch请求
                window.fetch = function(...args) {
                    const result = originalFetch.apply(this, args);
                    result.then(response => {
                        const authHeader = response.headers.get('authorization');
                        if (authHeader && authHeader.includes('Bearer') && !found) {
                            const token = authHeader.replace('Bearer ', '');
                            if (token.length > 20) {
                                found = true;
                                resolve(token);
                            }
                        }
                    }).catch(() => {});
                    return result;
                };

                // 拦截XMLHttpRequest
                window.XMLHttpRequest.prototype.open = function(...args) {
                    const xhr = this;
                    const originalSetRequestHeader = xhr.setRequestHeader;

                    xhr.setRequestHeader = function(name, value) {
                        if (name.toLowerCase() === 'authorization' && value.includes('Bearer') && !found) {
                            const token = value.replace('Bearer ', '');
                            if (token.length > 20) {
                                found = true;
                                resolve(token);
                            }
                        }
                        return originalSetRequestHeader.apply(this, args);
                    };

                    return originalXHR.apply(this, args);
                };

                // 5秒后恢复原始函数并返回null
                setTimeout(() => {
                    window.fetch = originalFetch;
                    window.XMLHttpRequest.prototype.open = originalXHR;
                    if (!found) {
                        resolve(null);
                    }
                }, 5000);
            });
        }

        // 从页面元素中查找Token
        function findTokenFromPageElements() {
            const tokenSelectors = [
                'input[value*="sk-"]',
                'input[placeholder*="token" i]',
                'code',
                'pre',
                '.token',
                '.api-key',
                '[data-testid*="token"]',
                '[data-testid*="api"]'
            ];

            for (const selector of tokenSelectors) {
                try {
                    const element = document.querySelector(selector);
                    if (element) {
                        const text = element.value || element.textContent || element.innerText || '';
                        if (text && text.length >= 20) {
                            return text.trim();
                        }
                    }
                } catch (e) {
                    console.log(`选择器 ${selector} 出错:`, e);
                }
            }
            return null;
        }

        // 从存储中查找Token
        function findTokenFromStorage() {
            try {
                // 检查localStorage
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    const value = localStorage.getItem(key);
                    if (value && (key.toLowerCase().includes('token') || key.toLowerCase().includes('auth'))) {
                        if (value.length >= 20) {
                            return value;
                        }
                    }
                }

                // 检查sessionStorage
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    const value = sessionStorage.getItem(key);
                    if (value && (key.toLowerCase().includes('token') || key.toLowerCase().includes('auth'))) {
                        if (value.length >= 20) {
                            return value;
                        }
                    }
                }
            } catch (e) {
                console.log('检查存储时出错:', e);
            }
            return null;
        }
    }

    // 等待页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();