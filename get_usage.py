import requests
import json
import base64
import re
import winreg
from typing import Dict, Optional, Any




import sqlite3
import os

db_path = os.path.join(os.getenv('APPDATA'), 'Cursor', 'User', 'globalStorage', 'state.vscdb')
# db_path = os.path.join(os.getenv('APPDATA'), 'Cursor nightly', 'User', 'globalStorage', 'state.vscdb')

def get_token():
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看表结构
        # cursor.execute("PRAGMA table_info(itemTable);")
        # cursor.execute("select key, value from itemTable where key='cursorAuth/cachedEmail'")
        # cursor.execute("select key, value from itemTable where key='cursorAuth/refreshToken'")
        cursor.execute("select key, value from itemTable where key='cursorAuth/accessToken'")
        # cursor.execute("select key, value from itemTable where key like 'cursorai/serverConfig%'")
        columns = cursor.fetchall()
        token = columns[0][1]
        print("itemTable 列信息:")
        for col in columns:
            print(col)
        print('当前token:', token)
        return token
    except sqlite3.Error as e:
        print("数据库错误:", str(e))
        return None
    finally:
        if conn:
            conn.close()


# 常量定义
NAME_LOWER = "cursor"
NAME_CAPITALIZE = "Cursor"
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# 基础请求头
BASE_HEADERS = {
    "User-Agent": USER_AGENT
}

def get_proxy():
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Internet Settings") as key:
            proxy_enable, _ = winreg.QueryValueEx(key, "ProxyEnable")
            proxy_server, _ = winreg.QueryValueEx(key, "ProxyServer")
            
            if proxy_enable and proxy_server:
                proxy_parts = proxy_server.split(":")
                if len(proxy_parts) == 2:
                    return {"http": f"http://{proxy_server}", "https": f"http://{proxy_server}"}
    except WindowsError:
        pass
    return {"http": None, "https": None}

def extract_user_id_from_jwt(token: str) -> str:
    """从JWT中提取用户ID"""
    try:
        payload = json.loads(base64.b64decode(token.split('.')[1] + '==').decode('utf-8'))
        match = re.search(r'auth0\|(.+)', payload.get('sub', ''))
        return match.group(1) if match else ''
    except:
        return ''

def get_usage(token):
    """获取使用量信息"""
    url = f"https://www.{NAME_LOWER}.com/api/usage"

    user_id = extract_user_id_from_jwt(token)
    cookie_id = user_id if user_id else "user_01OOOOOOOOOOOOOOOOOOOOOOOO"
    
    headers = BASE_HEADERS.copy()
    headers.update({
        "Cookie": f"Workos{NAME_CAPITALIZE}SessionToken={cookie_id}%3A%3A{token}"
    })

    # print(headers) 
    proxies = (get_proxy())
    # proxies = {'http': None, 'https': None}
    
    try:
        response = requests.get(url, headers=headers, timeout=10, proxies=proxies)
        response.raise_for_status()
        data = response.json()
        # print(data)
        
        return {
            "premium_usage": data.get("gpt-4", {}).get("numRequestsTotal", 0),
            "max_premium_usage": data.get("gpt-4", {}).get("maxRequestUsage", 999),
            "basic_usage": data.get("gpt-3.5-turbo", {}).get("numRequestsTotal", 0),
            "max_basic_usage": data.get("gpt-3.5-turbo", {}).get("maxRequestUsage", 999)
        }
    except requests.RequestException as e:
        print(f"获取使用量失败: {e}")
        return None

def get_stripe_profile(token):
    """获取用户订阅信息"""
    url = f"https://api2.{NAME_LOWER}.sh/auth/full_stripe_profile"
    
    headers = BASE_HEADERS.copy()
    headers.update({
        "Authorization": f"Bearer {token}"
    })
    
    try:
        response = requests.get(url, headers=headers, timeout=10, proxies=get_proxy())
        # print(response.text)
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print(f"获取订阅信息失败: {e}")
        return None

def get_auth0_userinfo(token: str) -> Optional[Dict[str, Any]]:
    """获取Auth0用户信息"""
    url = "https://www.cursor.com/api/auth/me"
    
    user_id = extract_user_id_from_jwt(token)
    print(user_id)
    cookie_id = user_id if user_id else "user_01OOOOOOOOOOOOOOOOOOOOOOOO"
    
    headers = BASE_HEADERS.copy()
    headers.update({
        "Authorization": f"Bearer {token}",
        "Cookie": f"Workos{NAME_CAPITALIZE}SessionToken={cookie_id}%3A%3A{token}"
    })
    
    try:
        response = requests.get(url, headers=headers, timeout=10, proxies=get_proxy())
        response.raise_for_status()
        data = response.json()

        return data
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except ValueError as e:
        print(f"JSON解析失败: {e}")
        return None


# 使用示例
if __name__ == "__main__":
    # jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSktTNkQ5Tkc4TUg5OTNaUVdGQ1YyVDZOIiwidGltZSI6IjE3MzkyMzM2MTMiLCJyYW5kb21uZXNzIjoiM2U3MzkxYjgtNGUwOC00OTZkIiwiZXhwIjo0MzMxMjMzNjEzLCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20ifQ.wnIvGkDL0uaRYsjtzJyNFVCZ_3dgEarf4HZQQrc7Urk"
    jwt_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlZUUktSR1gxUko0UkY0MDFWRkpUWk1EIiwidGltZSI6IjE3NDc5MTU2MzciLCJyYW5kb21uZXNzIjoiODUwYTE1ZDYtZDUyNC00YjFhIiwiZXhwIjoxNzUzMDk5NjM3LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20iLCJ0eXBlIjoic2Vzc2lvbiJ9.-yeB5SFkI1DeeCR-jj6ChiPPAS79Pj8pP1jl_0SlJ4Q"

    # jwt_token = get_token()
    print("\n=== 订阅信息 ===")
    print(f"用户ID: {jwt_token}")
    print(get_proxy())

    # 获取用户信息
    auth0_userinfo = get_auth0_userinfo(jwt_token)
    if auth0_userinfo:
        print("\n=== 用户信息 ===")
        print(f"邮箱: {auth0_userinfo.get('email')}")
        print(f"邮箱验证: {auth0_userinfo.get('email_verified')}")
        print(f"用户名: {auth0_userinfo.get('name') or '未设置'}")
        print(f"用户ID: {auth0_userinfo.get('sub')}")
        print(f"更新时间: {auth0_userinfo.get('updated_at')}")

    # 获取订阅信息
    profile = get_stripe_profile(jwt_token)
    if profile:
        print("\n=== 订阅信息 ===")
        print(f"用户ID: {extract_user_id_from_jwt(jwt_token)}")
        print(f"账户类型: {profile['membershipType']}")
        print(f"剩余天数: {profile['daysRemainingOnTrial']}")

    # 获取使用量
    usage = get_usage(jwt_token)
    if usage:
        print("\n=== 使用量信息 ===")
        print(f'Premium 使用情况: {usage["premium_usage"]}/{usage["max_premium_usage"]}')
        print(f'Basic   使用情况: {usage["basic_usage"]}/{usage["max_basic_usage"]}')
    
        
