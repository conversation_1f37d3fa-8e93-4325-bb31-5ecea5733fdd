# from DrissionPage import Chromium, ChromiumOptions


# co = ChromiumOptions()
# co.set_browser_path(r'C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1067\chrome-win\chrome.exe')
# co.add_extension("turnstilePatch")
# co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.69 Safari/537.36')   
# co.set_pref('credentials_enable_service', False)
# co.set_argument('--hide-crash-restore-bubble')
# co.auto_port()

# # 创建ChromiumPage对象（自动启动浏览器）
# browser = Chromium(co)

# tab = browser.latest_tab

# # 先访问主域名
# tab.get('https://www.cursor.com')
# # user_01JSE91P7Z7J998PKCG5MBPDRH::
# # 设置cookie
# cookie = {
#     'name': 'WorkosCursorSessionToken',
#     'value': 'user_01JSE91P7Z7J998PKCG5MBPDRH::eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlNFOTFQN1o3Sjk5OFBLQ0c1TUJQRFJIIiwidGltZSI6IjE3NDUzMDk3MzMiLCJyYW5kb21uZXNzIjoiMDU3ZTM0OTktNGUxYi00YTlkIiwiZXhwIjoxNzQ1MzE1MTMzLCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20ifQ.6Rk_pJuCJLReI2GvajsHRbrVvURDltZbsCVfVp1qM_s',
#     'domain': '.cursor.com',
#     'path': '/'
# }
# tab.set.cookies(cookie)

# # 访问设置页面
# tab.get('https://www.cursor.com/settings')

# # 等待页面加载完成（等待3秒）
# tab.wait(3)

# # 获取页面标题
# title = tab.title
# print(f'页面标题: {title}')

# # 获取当前URL
# current_url = tab.url
# print(f'当前URL: {current_url}')

# # 保持浏览器窗口打开，直到用户手动关闭
# input('按回车键关闭浏览器...')

# # 关闭浏览器
# browser.quit()

import sqlite3
import os
import datetime

def read_sqlite_database_and_save(db_path, output_filename="warp_db_content.txt"):
    """
    连接到 SQLite 数据库，读取所有表的名称和前几行数据，
    并将所有输出保存到指定的文件中。
    """
    output_lines = [] # 用于存储所有将要写入文件的行

    # 记录脚本开始时间
    start_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    output_lines.append(f"--- 脚本执行开始: {start_time} ---")
    output_lines.append(f"尝试读取数据库: {db_path}")
    output_lines.append(f"结果将保存到文件: {output_filename}")
    output_lines.append(f"请确保 Warp 应用程序已完全关闭，否则数据库文件可能被锁定。")
    output_lines.append("-" * 50)

    if not os.path.exists(db_path):
        output_lines.append(f"错误：数据库文件不存在于此路径: {db_path}")
        print(f"错误：数据库文件不存在于此路径: {db_path}") # 也在控制台提示
        # 将当前输出写入文件
        with open(output_filename, 'w', encoding='utf-8') as f:
            f.write("\n".join(output_lines))
        return

    conn = None # 初始化 conn 为 None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        output_lines.append(f"成功连接到数据库：{db_path}")

        # 1. 获取所有表的名称
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if not tables:
            output_lines.append("数据库中没有找到任何表。")
        else:
            output_lines.append("\n数据库中找到的表：")
            for table_name_tuple in tables:
                table_name = table_name_tuple[0]
                output_lines.append(f"- {table_name}")

                # 2. 尝试读取每个表的前5行数据
                output_lines.append(f"\n--- 表 '{table_name}' 的前5行数据 ---")
                try:
                    cursor.execute(f"PRAGMA table_info({table_name});")
                    columns_info = cursor.fetchall()
                    
                    if not columns_info:
                        output_lines.append(f"  (无法获取表 '{table_name}' 的列信息)")
                        continue

                    column_names = [col[1] for col in columns_info]

                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 5;")
                    rows = cursor.fetchall()

                    if not rows:
                        output_lines.append(f"  表 '{table_name}' 中没有数据或无法读取。")
                    else:
                        # 打印列头
                        output_lines.append("  " + " | ".join(column_names))
                        output_lines.append("  " + "-" * (sum(len(c) for c in column_names) + 3 * (len(column_names) - 1)))
                        # 打印数据
                        for row in rows:
                            # 确保所有数据项都转换为字符串，以避免编码问题
                            row_str = " | ".join(str(item) for item in row)
                            output_lines.append(f"  {row_str}")

                except sqlite3.OperationalError as e:
                    output_lines.append(f"  错误：无法读取表 '{table_name}' 的数据。可能原因是: {e}")
                except Exception as e:
                    output_lines.append(f"  读取表 '{table_name}' 时发生未知错误: {e}")

    except sqlite3.Error as e:
        output_lines.append(f"连接或操作 SQLite 数据库时发生错误: {e}")
        print(f"连接或操作 SQLite 数据库时发生错误: {e}") # 也在控制台提示
    finally:
        if conn:
            conn.close()
            output_lines.append("\n数据库连接已关闭。")
        
        # 记录脚本结束时间
        end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        output_lines.append(f"\n--- 脚本执行完毕: {end_time} ---")
        output_lines.append("如果仍然没有看到数据，可能需要更深入地检查 Warp 应用程序的行为或文件权限。")
        output_lines.append("在某些情况下，数据可能在其他地方，或者在应用程序运行时才会完全加载到数据库中。")

        # 将所有收集到的行写入文件
        try:
            with open(output_filename, 'w', encoding='utf-8') as f:
                f.write("\n".join(output_lines))
            print(f"\n数据库内容已成功保存到文件: {output_filename}")
        except Exception as e:
            print(f"\n错误：无法将内容保存到文件 '{output_filename}': {e}")
            print("以下是应该保存到文件的内容：")
            print("\n".join(output_lines))


# --- 使用示例 ---
if __name__ == "__main__":
    # 请将此路径替换为你的实际 warp.sqlite 文件路径
    db_file_path = r"C:\Users\<USER>\AppData\Local\warp\Warp\data\warp.sqlite"
    
    # 你可以指定输出文件的名称和路径
    output_report_file = "warp_db_analysis_report.txt" 
    
    print(f"--- 正在尝试读取 Warp 数据库内容并保存到 '{output_report_file}' ---")
    read_sqlite_database_and_save(db_file_path, output_report_file)

