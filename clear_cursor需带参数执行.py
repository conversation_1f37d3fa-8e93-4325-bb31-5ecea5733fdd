import os
import shutil
import platform
import logging
import argparse
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 应用程序类型常量
VSCODE = "vscode"
CURSOR = "cursor"
CURSOR_NIGHTLY = "cursor_nightly"
ALL = "all"

def get_appdata_path(app_type):
    """根据操作系统和应用类型返回合适的AppData路径"""
    system = platform.system()
    
    if app_type == VSCODE:
        app_name = "Code"
    elif app_type == CURSOR:
        app_name = "Cursor"
    elif app_type == CURSOR_NIGHTLY:
        app_name = "Cursor Nightly"
    else:
        logger.error(f"不支持的应用类型: {app_type}")
        return None
    
    if system == "Windows":
        return os.path.join(os.environ.get("APPDATA", ""), app_name)
    elif system == "Darwin":  # macOS
        return os.path.expanduser(f"~/Library/Application Support/{app_name}")
    elif system == "Linux":
        return os.path.expanduser(f"~/.config/{app_name}")
    else:
        logger.error(f"不支持的操作系统: {system}")
        return None

def get_all_app_paths():
    """获取所有支持的应用程序路径"""
    paths = []
    for app_type in [VSCODE, CURSOR, CURSOR_NIGHTLY]:
        path = get_appdata_path(app_type)
        if path:
            paths.append((app_type, path))
    return paths

def clear_cache_files(directory, app_type):
    """清理目录中包含'cache'的文件夹"""
    if not os.path.exists(directory):
        logger.warning(f"目录不存在: {directory}")
        return False
    
    try:
        # 使用pathlib遍历目录，查找包含'cache'的文件夹
        count = 0
        total_size = 0
        
        for path in Path(directory).rglob("*"):
            if path.is_dir() and "cache" in path.name.lower():
                # 计算文件夹大小
                folder_size = sum(f.stat().st_size for f in path.glob('**/*') if f.is_file())
                total_size += folder_size
                
                # 记录并删除文件夹
                logger.info(f"正在删除: {path} ({folder_size / (1024*1024):.2f} MB)")
                shutil.rmtree(path, ignore_errors=True)
                count += 1
        
        if count > 0:
            logger.info(f"共清理 {count} 个缓存文件夹，释放 {total_size / (1024*1024):.2f} MB 空间")
        else:
            logger.info(f"未找到缓存文件夹")
        return True
    
    except Exception as e:
        logger.error(f"清理缓存时出错: {str(e)}")
        return False

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="清理VS Code、Cursor和Cursor Nightly的缓存文件")
    parser.add_argument("--app", "-a", choices=[VSCODE, CURSOR, CURSOR_NIGHTLY, ALL], default=ALL,
                        help="指定要清理的应用程序 (默认: 清理所有支持的应用)")
    args = parser.parse_args()
    
    logger.info("开始清理缓存文件...")
    
    if args.app == ALL:
        # 清理所有应用
        all_paths = get_all_app_paths()
        success = True
        
        for app_type, app_path in all_paths:
            app_name = "VS Code" if app_type == VSCODE else "Cursor" if app_type == CURSOR else "Cursor Nightly"
            logger.info(f"正在清理 {app_name} 缓存...")
            logger.info(f"目标目录: {app_path}")
            
            if not clear_cache_files(app_path, app_type):
                success = False
    else:
        # 清理特定应用
        app_name = "VS Code" if args.app == VSCODE else "Cursor" if args.app == CURSOR else "Cursor Nightly"
        logger.info(f"正在清理 {app_name} 缓存...")
        
        appdata_path = get_appdata_path(args.app)
        if not appdata_path:
            return
        
        logger.info(f"目标目录: {appdata_path}")
        success = clear_cache_files(appdata_path, args.app)
    
    if success:
        logger.info("缓存清理完成!")
    else:
        logger.error("缓存清理过程中发生错误!")

if __name__ == "__main__":
    main()
