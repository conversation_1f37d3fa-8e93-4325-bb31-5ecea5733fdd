import os
import shutil
import platform
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)



app_name = "Cursor"
# app_name = "Cursor Nightly"
# app_name = "Code"


def get_appdata_path():
    """根据操作系统返回合适的AppData路径"""
    system = platform.system()
    
    if system == "Windows":
        return os.path.join(os.environ.get("APPDATA", ""), app_name)
    elif system == "Darwin":  # macOS
        return os.path.expanduser(f"~/Library/Application Support/{app_name}")
    elif system == "Linux":
        return os.path.expanduser(f"~/.config/{app_name}")
    else:
        logger.error(f"不支持的操作系统: {system}")
        return None

def clear_cache_files(directory):
    """清理目录中包含'cache'的文件夹"""
    if not os.path.exists(directory):
        logger.warning(f"目录不存在: {directory}")
        return False
    
    try:
        # 使用pathlib遍历目录，查找包含'cache'的文件夹
        count = 0
        total_size = 0
        
        for path in Path(directory).rglob("*"):
            if path.is_dir() and "cache" in path.name.lower():
                # 计算文件夹大小
                folder_size = sum(f.stat().st_size for f in path.glob('**/*') if f.is_file())
                total_size += folder_size
                
                # 记录并删除文件夹
                logger.info(f"正在删除: {path} ({folder_size / (1024*1024):.2f} MB)")
                shutil.rmtree(path, ignore_errors=True)
                count += 1
        
        logger.info(f"共清理 {count} 个缓存文件夹，释放 {total_size / (1024*1024):.2f} MB 空间")
        return True
    
    except Exception as e:
        logger.error(f"清理缓存时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始清理VS Code缓存文件...")
    
    # 获取AppData路径
    appdata_path = get_appdata_path()
    if not appdata_path:
        return
    
    logger.info(f"目标目录: {appdata_path}")
    
    # 清理缓存
    success = clear_cache_files(appdata_path)
    
    if success:
        logger.info("缓存清理完成!")
    else:
        logger.error("缓存清理失败!")

if __name__ == "__main__":
    main()