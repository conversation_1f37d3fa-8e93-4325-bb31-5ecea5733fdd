from DrissionPage import Chromium, ChromiumOptions
import time
import random
import winreg
import requests
import logging
from datetime import datetime

# 配置日志
def setup_logger():
    # 创建logger对象
    logger = logging.getLogger('voapi')
    logger.setLevel(logging.INFO)
    
    # 使用固定的日志文件名
    log_filename = 'voapi.log'
    
    # 创建文件处理器，使用 'w' 模式覆盖旧文件
    file_handler = logging.FileHandler(log_filename, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def get_proxy_settings():
    """
    获取 Windows 10 的手动代理设置。

    Returns:
        tuple: 包含两个元素的元组：
            - bool: 如果启用了手动代理设置，则为 True，否则为 False。
            - str: 代理服务器地址和端口，例如 "127.0.0.1:8080"，如果未启用代理则为空字符串。
    """

    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                           r'Software\Microsoft\Windows\CurrentVersion\Internet Settings') as key:
            proxy_enable, _ = winreg.QueryValueEx(key, 'ProxyEnable')
            if proxy_enable == 1:
                proxy_server, _ = winreg.QueryValueEx(key, 'ProxyServer')
                return True, proxy_server
            else:
                return False, ""
    except Exception as e:
        print(f"Error getting proxy settings: {e}")
        return False, ""

def AnPush_send(title, content, channel):
    proxy_enabled, proxy_server = get_proxy_settings()

    if proxy_enabled:
        proxies = {
            'http': f'http://{proxy_server}',
            'https': f'http://{proxy_server}'
        }
        print('代理端口：', proxies)
    else:
        proxies = {}
        print('未启用代理')
    payload = {
        "title": title,
        "content": content,
        "channel": channel
    }

    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }

    response = requests.post(url, headers=headers, data=payload, proxies=proxies)
    
    return response


def handle_turnstile(tab):
    """处理 Turnstile 验证"""
    logger.info("开始处理 Turnstile 验证")
    
    for attempt in range(15):
        try:
            challengeCheck = (tab.ele("@name=cf-turnstile-response", timeout=2)
                                .parent()
                                .shadow_root
                                .ele("tag:iframe")
                                .ele("tag:body")
                                .sr("tag:input"))
                            
            if challengeCheck:
                logger.info("验证框加载完成")
                time.sleep(random.uniform(2, 4))  # 增加等待时间
                challengeCheck.click()
                logger.info("验证按钮已点击，等待验证完成...")
                time.sleep(3)  # 增加验证后等待时间
                return True
                
            time.sleep(2)
            logger.warning(f"第 {attempt + 1} 次尝试未找到验证框")
            
        except Exception as e:
            # logger.error(f"验证处理出错: {str(e)}")
            pass
            
    logger.error("验证失败：已达到最大尝试次数(15次)")
    return False

def try_connect_website(tab, url, max_retries=3, timeout=30):
    """尝试连接网站
    
    Args:
        tab: 浏览器标签页
        url: 目标网址
        max_retries: 最大重试次数
        timeout: 超时时间(秒)
    
    Returns:
        bool: 是否连接成功
    """
    for i in range(max_retries):
        try:
            logger.info(f"正在尝试第 {i+1} 次连接...")
            tab.get(url, timeout=timeout)
            return True
        except Exception as e:
            logger.error(f"连接失败: {str(e)}")
            if i < max_retries - 1:
                logger.info("等待重试...")
                time.sleep(3)
            continue
    return False

options = ChromiumOptions()
options.add_extension("turnstilePatch")
options.set_argument('--disable-blink-features=AutomationControlled')
options.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.6723.92 Safari/537.36')
options.auto_port()                 #自动分配端口
options.ignore_certificate_errors() # 忽略证书错误
# options.headless()  # 无头模式
options.set_pref('credentials_enable_service', False)  # 隐藏是否保存密码的提示

# 初始化日志
logger = setup_logger()

api_token= 'HKY5HDE82K6IRFQ0X00UXWDKYYQGOF'
channel = '55228'
url = f"https://api.anpush.com/push/{api_token}"
username = 'linuxdo_3221'
password = 'linuxdodo'

browser = Chromium(options)
tab = browser.latest_tab
tab.run_js("try { turnstile.reset() } catch(e) { }")

try:
    logger.info('开始连接目标网站')
    if not try_connect_website(tab, 'https://demo.voapi.top/login'):
        logger.error("无法连接到目标网站,程序退出")
        AnPush_send(title='VOAPI签到失败', content='无法连接到目标网站', channel=channel)
        logger.info('访问失败通知已发送')
        exit(1)
        
    time.sleep(2)
    try:
        tab.ele('xpath://*[@id="dialog-0"]/div/div[1]/button').click()
        logger.info('成功关闭公告弹窗')
    except Exception as e:
        logger.warning(f'关闭公告弹窗失败：{e}，继续执行后续操作')

    if not handle_turnstile(tab):
        logger.error("初始验证失败，但继续执行后续操作")

    tab.ele('@id=username').input(username)
    logger.info(f'输入用户名: {username}')
    time.sleep(1)

    tab.ele('@id=password').input(password)  # 密码脱敏
    logger.info('输入密码: ********')
    time.sleep(1)

    tab.ele('@type=submit').click()
    logger.info('点击登录按钮')

    # time.sleep(2)
    # for attempt in range(5):
    #     try:
    #         logger.info(f"第 {attempt + 1} 次尝试点击个人资料...")
    #         profile_link = tab.ele('xpath://*[@id="root"]/section/header/section/div/div/div/div[1]/div[2]/ul/a[10]/li', timeout=30)
    #         if not profile_link:
    #             logger.warning("未找到个人资料链接")
    #             time.sleep(2)
    #             continue
    #         profile_link.click()
    #         logger.info('成功进入个人资料页面')
    #         break
    #     except Exception as e:
    #         logger.error(f'点击个人资料出错：{e}')
    #         if attempt < 4:
    #             logger.info("等待2秒后重试...")
    #             time.sleep(2)
    #         else:
    #             logger.error("点击个人资料失败：已达到最大尝试次数(5次)")

    if not handle_turnstile(tab):
        logger.error("签到前验证失败")

    time.sleep(2)
    try:
        # tab.ele('xpath://*[@id="semiTabPanelpersonal"]/div/div/section/main/div/div[1]/div[3]/div/div[2]/button').click()
        tab.ele('xpath://*[@id="root"]/section/section/section/main/div[1]/div/div/section/main/div/div[1]/div[3]/div/div[2]/button').click()
        logger.info('成功点击签到按钮')
    except Exception as e:
        logger.error(f'点击签到按钮失败: {e}')

    time.sleep(2)

    try:
        # 增加重试机制
        for retry in range(3):
            try:
                # 使用更可靠的选择器
                balance = tab.ele('xpath://*[@id="root"]/section/section/section/main/div[1]/div/div/section/main/div/div[1]/div[3]/div/div[1]/table/tbody/tr[1]/td/span')
                # if not balance:
                #     balance = tab.ele('xpath://td/span[contains(text(),"$")]', timeout=10)
                
                if balance:
                    balance_text = balance.text
                    logger.info(f'当前余额: {balance_text}')

                    if float(balance_text.strip('$')) > 0:
                        logger.info('签到成功！')
                        AnPush_send(title='VOAPI签到成功', content='余额：'+balance_text, channel=channel)
                        logger.info('推送通知已发送')
                        break
                    else:
                        logger.warning('签到可能失败：余额为0')
                        AnPush_send(title='VOAPI签到失败', content='余额：'+balance_text, channel=channel)
                        logger.info('失败通知已推送')
                        break
                
                time.sleep(2)
                logger.warning(f"第 {retry + 1} 次尝试获取余额失败")
                
            except Exception as e:
                logger.error(f'获取余额时出错：{e}')
                if retry < 2:
                    time.sleep(2)
                    continue
                AnPush_send(title='VOAPI签到异常', content='无法获取余额信息', channel=channel)
                
    except Exception as e:
        logger.error(f'处理余额信息时发生异常: {e}')

finally:
    try:
        browser.quit()
        logger.info("浏览器资源已释放，程序正常结束")
    except Exception as e:
        logger.error(f"关闭浏览器时出错: {e}")
