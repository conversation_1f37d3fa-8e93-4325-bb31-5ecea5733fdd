import jwt
import time

def get_expiration_date_from_token(token):
  """
  从 JWT 中提取过期时间。

  Args:
    token: JWT 字符串。

  Returns:
    一个 datetime 对象，表示过期时间。 如果 token 无效或缺少 exp 声明，则返回 None。
  """
  try:
    # 解码 JWT (不验证签名，因为我们只关心过期时间)
    payload = jwt.decode(token, options={"verify_signature": False})  # 注意: 这里关闭了签名验证

    if 'exp' in payload:
      expiration_timestamp = payload['exp']
      expiration_date = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expiration_timestamp)) # 将时间戳转换为日期时间格式
      return expiration_date
    else:
      return None  # 缺少 'exp' 声明

  except jwt.exceptions.DecodeError:
    return None  # 无效的 JWT 格式
  except Exception as e:
    print(f"发生错误: {e}")
    return None

# 示例 JWT (你可以替换为你自己的 JWT)
token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhdXRoMHx1c2VyXzAxSlRNWUhSM0ZaR0MzMUIyVEFYRVFZUDQ5IiwidGltZSI6IjE3NDY2NzQzMjciLCJyYW5kb21uZXNzIjoiZWIzZmJhNmYtYzY0ZC00YzJkIiwiZXhwIjoxNzUxODU4MzI3LCJpc3MiOiJodHRwczovL2F1dGhlbnRpY2F0aW9uLmN1cnNvci5zaCIsInNjb3BlIjoib3BlbmlkIHByb2ZpbGUgZW1haWwgb2ZmbGluZV9hY2Nlc3MiLCJhdWQiOiJodHRwczovL2N1cnNvci5jb20ifQ.oOYTeByCCF5QciQ0ygDyjbcO15reopn9_cyb_Pomm3U"

expiration_date = get_expiration_date_from_token(token)

if expiration_date:
  print(f"Token 过期时间: {expiration_date}")
else:
  print("无法获取 Token 过期时间")
