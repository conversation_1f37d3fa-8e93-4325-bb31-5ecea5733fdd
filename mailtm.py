import requests
import json
import time
from typing import List, Dict, Any, Optional, Union

class MailTM:
    """
    Mail.tm API 客户端，用于创建临时邮箱、接收和删除邮件
    
    基于 https://docs.mail.tm/ 的 API 实现
    """
    
    BASE_URL = "https://api.mail.tm"
    
    def __init__(self):
        self.token = None
        self.account_id = None
        self.email_address = None
    
    def get_domains(self) -> List[Dict[str, Any]]:
        """获取可用的域名列表"""
        response = requests.get(f"{self.BASE_URL}/domains")
        response.raise_for_status()
        return response.json()["hydra:member"]
    
    def create_account(self, prefix: str, password: str = "password123") -> Dict[str, Any]:
        """
        创建指定前缀的邮箱账户
        
        Args:
            prefix: 邮箱前缀（@前面的部分）
            password: 账户密码，默认为 "password123"
            
        Returns:
            包含账户信息的字典
        """
        # 获取可用域名
        domains = self.get_domains()
        if not domains:
            raise Exception("没有可用的域名")
        
        domain = domains[0]["domain"]
        email = f"{prefix}@{domain}"
        
        # 创建账户
        data = {
            "address": email,
            "password": password
        }
        
        response = requests.post(
            f"{self.BASE_URL}/accounts",
            json=data
        )
        
        if response.status_code == 422:
            error_detail = response.json().get("violations", [{}])[0].get("message", "未知错误")
            raise Exception(f"创建账户失败: {error_detail}")
        
        response.raise_for_status()
        account_data = response.json()
        
        # 保存账户信息
        self.account_id = account_data["id"]
        self.email_address = email
        
        # 获取认证令牌
        self.get_token(email, password)
        
        return account_data
    
    def get_token(self, email: str, password: str) -> str:
        """
        获取认证令牌
        
        Args:
            email: 邮箱地址
            password: 账户密码
            
        Returns:
            认证令牌
        """
        data = {
            "address": email,
            "password": password
        }
        
        response = requests.post(
            f"{self.BASE_URL}/token",
            json=data
        )
        response.raise_for_status()
        
        token_data = response.json()
        self.token = token_data["token"]
        return self.token
    
    def get_messages(self) -> List[Dict[str, Any]]:
        """
        获取邮件列表
        
        Returns:
            邮件列表
        """
        if not self.token:
            raise Exception("未登录，请先创建账户或获取令牌")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        response = requests.get(
            f"{self.BASE_URL}/messages",
            headers=headers
        )
        response.raise_for_status()
        
        return response.json()["hydra:member"]
    
    def get_message(self, message_id: str) -> Dict[str, Any]:
        """
        获取特定邮件的详细内容
        
        Args:
            message_id: 邮件ID
            
        Returns:
            邮件详情
        """
        if not self.token:
            raise Exception("未登录，请先创建账户或获取令牌")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        response = requests.get(
            f"{self.BASE_URL}/messages/{message_id}",
            headers=headers
        )
        response.raise_for_status()
        
        return response.json()
    
    def delete_message(self, message_id: str) -> bool:
        """
        删除特定邮件
        
        Args:
            message_id: 邮件ID
            
        Returns:
            是否删除成功
        """
        if not self.token:
            raise Exception("未登录，请先创建账户或获取令牌")
        
        headers = {"Authorization": f"Bearer {self.token}"}
        response = requests.delete(
            f"{self.BASE_URL}/messages/{message_id}",
            headers=headers
        )
        
        return response.status_code == 204
    
    def wait_for_message(self, timeout: int = 30, check_interval: int = 2) -> Optional[Dict[str, Any]]:
        """
        等待接收新邮件
        
        Args:
            timeout: 超时时间（秒）
            check_interval: 检查间隔（秒）
            
        Returns:
            收到的邮件，如果超时则返回 None
        """
        start_time = time.time()
        initial_messages = self.get_messages()
        initial_count = len(initial_messages)
        
        print(f"等待接收邮件到 {self.email_address}...")
        
        while time.time() - start_time < timeout:
            current_messages = self.get_messages()
            if len(current_messages) > initial_count:
                new_message = current_messages[0]  # 最新的邮件在列表前面
                message_detail = self.get_message(new_message["id"])
                return message_detail
            
            time.sleep(check_interval)
        
        return None
    
    def delete_all_messages(self) -> Dict[str, Any]:
        """
        删除所有邮件
        
        Returns:
            包含删除结果的字典：{'total': 总邮件数, 'deleted': 成功删除数, 'failed': 失败数}
        """
        if not self.token:
            raise Exception("未登录，请先创建账户或获取令牌")
        
        messages = self.get_messages()
        total = len(messages)
        deleted = 0
        failed = 0
        
        print(f"开始删除所有邮件，共 {total} 封...")
        
        for message in messages:
            message_id = message["id"]
            if self.delete_message(message_id):
                deleted += 1
            else:
                failed += 1
        
        result = {
            "total": total,
            "deleted": deleted,
            "failed": failed
        }
        
        print(f"删除完成：成功 {deleted} 封，失败 {failed} 封")
        return result
    
    def login(self, email: str, password: str) -> Dict[str, Any]:
        """
        登录到已有的邮箱账户
        
        Args:
            email: 邮箱地址
            password: 账户密码
            
        Returns:
            包含账户信息的字典
        """
        self.email_address = email
        
        # 获取认证令牌
        token = self.get_token(email, password)
        
        print(f"成功登录到邮箱: {email}")
        return {"address": email, "token": token}


# 修改主程序部分
if __name__ == "__main__":
    mail = MailTM()
    
    print("Mail.tm 临时邮箱工具")
    print("=" * 30)
    
    # 邮箱登录或创建
    email_choice = input("选择操作：\n1. 创建新邮箱\n2. 登录已有邮箱\n请输入选项 (1/2): ")
    
    try:
        if email_choice == "1":
            # 创建新邮箱
            prefix = input("请输入邮箱前缀 (默认: nezhanaohai): ") or "nezhanaohai"
            password = input("请输入密码 (默认: password123): ") or "password123"
            
            account = mail.create_account(prefix, password)
            print(f"创建的邮箱: {mail.email_address}")
            print(f"账户ID: {mail.account_id}")
            print(f"请记住您的邮箱地址和密码，以便下次登录")
            
        elif email_choice == "2":
            # 登录已有邮箱
            email = input("请输入邮箱地址: ")
            password = input("请输入密码 (默认: password123): ") or "password123"
            
            mail.login(email, password)
        else:
            print("无效的选项，程序退出")
            exit(1)
        
        # 选择操作
        print("\n选择操作：")
        print("1. 等待接收邮件")
        print("2. 删除所有邮件")
        print("3. 退出程序")
        
        action_choice = input("请输入选项 (1/2/3): ")
        
        if action_choice == "1":
            # 等待接收邮件
            wait_time = input("请输入等待时间(秒) (默认: 60): ") or "60"
            wait_time = int(wait_time)
            
            message = mail.wait_for_message(timeout=wait_time)
            
            if message:
                print("\n收到新邮件:")
                print(f"发件人: {message['from']['address']}")
                print(f"主题: {message['subject']}")
                print(f"内容:\n{message['text']}")
                
                delete_choice = input("\n是否删除此邮件? (y/n): ")
                if delete_choice.lower() == "y":
                    if mail.delete_message(message["id"]):
                        print(f"邮件已删除 (ID: {message['id']})")
                    else:
                        print("邮件删除失败")
            else:
                print(f"\n{wait_time}秒内未收到新邮件")
                
        elif action_choice == "2":
            # 删除所有邮件
            confirm = input("确定要删除所有邮件吗? (y/n): ")
            if confirm.lower() == "y":
                mail.delete_all_messages()
        
        elif action_choice == "3":
            print("程序退出")
        
        else:
            print("无效的选项，程序退出")
    
    except Exception as e:
        print(f"错误: {e}")
    
    input("\n按回车键退出...")
