# import logging
# import time
# import uuid
# import base64
# import hashlib
# import secrets
# import requests
# from typing import Optional, Tuple


# def get_cursor_session_token(tab, max_attempts: int = 3, retry_interval: int = 2) -> Optional[Tuple[str, str]]:
#     """
#     获取Cursor会话token
    
#     Args:
#         tab: 浏览器标签页对象
#         max_attempts: 最大尝试次数
#         retry_interval: 重试间隔(秒)
        
#     Returns:
#         Tuple[str, str] | None: 成功返回(userId, accessToken)元组，失败返回None
#     """
#     logging.info("开始获取会话令牌")
    
#     # 参数验证
#     if not tab:
#         logging.error("浏览器标签页对象不能为空")
#         return None
        
#     # 确保浏览器标签页处于可用状态
#     try:
#         current_url = tab.get_current_url()
#         logging.info(f"当前浏览器标签页URL: {current_url}")
#     except Exception as e:
#         logging.error(f"浏览器标签页不可用: {str(e)}")
#         return None
    
#     # 首先尝试使用UUID深度登录方式
#     logging.info("尝试使用深度登录方式获取token")
    
#     def _generate_pkce_pair():
#         """生成PKCE验证对"""
#         code_verifier = secrets.token_urlsafe(43)
#         code_challenge_digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
#         code_challenge = base64.urlsafe_b64encode(code_challenge_digest).decode('utf-8').rstrip('=')    
#         return code_verifier, code_challenge
    
#     attempts = 0
#     while attempts < max_attempts:
#         try:
#             verifier, challenge = _generate_pkce_pair()
#             id = uuid.uuid4()
#             client_login_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={id}&mode=login"
            
#             logging.info(f"访问深度登录URL: {client_login_url}")
#             tab.get(client_login_url)
#             # save_screenshot(tab, f"deeplogin_attempt_{attempts}")
            
#             if tab.ele("xpath=//span[contains(text(), 'Yes, Log In')]", timeout=5):
#                 logging.info("点击确认登录按钮")
#                 tab.ele("xpath=//span[contains(text(), 'Yes, Log In')]").click()
#                 time.sleep(1.5)
                
#                 auth_poll_url = f"https://api2.cursor.sh/auth/poll?uuid={id}&verifier={verifier}"
#                 headers = {
#                     "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
#                     "Accept": "*/*"
#                 }
                
#                 logging.info(f"轮询认证状态: {auth_poll_url}")
#                 response = requests.get(auth_poll_url, headers=headers, timeout=5)
                
#                 if response.status_code == 200:
#                     data = response.json()
#                     accessToken = data.get("accessToken", None)
#                     authId = data.get("authId", "")
                    
#                     if accessToken:
#                         userId = ""
#                         if len(authId.split("|")) > 1:
#                             userId = authId.split("|")[1]
                        
#                         logging.info("成功获取账号token和userId")
#                         return userId, accessToken
#                 else:
#                     logging.error(f"API请求失败，状态码: {response.status_code}")
#             else:
#                 logging.warning("未找到登录确认按钮")
                
#             attempts += 1
#             if attempts < max_attempts:
#                 wait_time = retry_interval * attempts  # 逐步增加等待时间
#                 logging.warning(f"第 {attempts} 次尝试未获取到token，{wait_time}秒后重试...")
#                 # save_screenshot(tab, f"token_attempt_{attempts}")
#                 time.sleep(wait_time)
                
#         except Exception as e:
#             logging.error(f"深度登录获取token失败: {str(e)}")
#             attempts += 1
#             # save_screenshot(tab, f"token_error_{attempts}")
#             if attempts < max_attempts:
#                 wait_time = retry_interval * attempts
#                 logging.warning(f"将在 {wait_time} 秒后重试...")
#                 time.sleep(wait_time)
    
#     logging.error(f"经过 {max_attempts} 次尝试后仍未获取到token")
#     return None


# def save_screenshot(tab, filename):
#     """
#     保存浏览器标签页截图
    
#     Args:
#         tab: 浏览器标签页对象
#         filename: 截图文件名（不含扩展名）
#     """
#     try:
#         filepath = f"logs/screenshots/{filename}.png"
#         tab.screenshot(filepath)
#         logging.info(f"截图已保存: {filepath}")
#     except Exception as e:
#         logging.error(f"保存截图失败: {str(e)}")


# if __name__ == "__main__":
#     # 需要导入并初始化浏览器标签页对象
#     try:
#         from browser_automation import create_browser_tab
        
#         # 配置日志
#         logging.basicConfig(
#             level=logging.INFO,
#             format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#             handlers=[
#                 logging.FileHandler("logs/token_trans.log"),
#                 logging.StreamHandler()
#             ]
#         )
        
#         # 创建浏览器标签页
#         browser_tab = create_browser_tab()
        
#         # 获取token
#         result = get_cursor_session_token(browser_tab)
#         if result:
#             user_id, access_token = result
#             print(f"用户ID: {user_id}")
#             print(f"访问令牌: {access_token[:10]}...")  # 只显示令牌前10位，保护隐私
#         else:
#             print("获取令牌失败")
            
#         # 关闭浏览器
#         browser_tab.close()
#     except ImportError:
#         print("请先安装browser_automation模块或提供浏览器标签页对象")
#     except Exception as e:
#         print(f"运行过程中发生错误: {str(e)}")


import requests
import uuid
import hashlib
import base64
import secrets
import time

def get_cursor_token_via_api():
    # 生成PKCE验证对
    code_verifier = secrets.token_urlsafe(43)
    code_challenge_digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
    code_challenge = base64.urlsafe_b64encode(code_challenge_digest).decode('utf-8').rstrip('=')
    
    # 生成UUID
    request_id = uuid.uuid4()
    
    # 第一步：模拟访问登录页 - 这里可能只需要记录参数
    login_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={code_challenge}&uuid={request_id}&mode=login"
    print(f"登录URL (可能需要手动访问): {login_url}")
    
    # 第二步：模拟确认登录 - 这是关键挑战
    # 可能需要找到一个API端点来模拟确认按钮点击
    # confirm_response = requests.post("https://api2.cursor.sh/auth/confirm", 
    #                               json={"uuid": str(request_id), "confirm": True},
    #                               headers={"Accept": "*/*"})
    
    # 手动确认提示
    input("请在浏览器中访问上面的URL并点击确认登录按钮，然后按Enter继续...")
    
    # 第三步：轮询获取token
    poll_url = f"https://api2.cursor.sh/auth/poll?uuid={request_id}&verifier={code_verifier}"
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "*/*"
    }
    
    # 方式1：使用Session（推荐）
    session = requests.Session()
    session.trust_env = False  # 禁用环境变量中的代理设置
    
    # 方式2：直接在请求中设置proxy=None（也可以工作）
    # response = requests.get(poll_url, headers=headers, timeout=5, proxies=None)
    
    # 多次尝试轮询
    max_attempts = 5
    for attempt in range(max_attempts):
        try:
            # 使用session方式
            response = session.get(poll_url, headers=headers, timeout=5)
            
            # 或者使用proxy=None方式
            # response = requests.get(poll_url, headers=headers, timeout=5, proxies=None)
            
            if response.status_code == 200:
                data = response.json()
                if "accessToken" in data:
                    return data
            print(f"轮询尝试 {attempt+1}/{max_attempts}...")
            time.sleep(2)
        except Exception as e:
            print(f"轮询出错: {str(e)}")
    
    return None

# 使用函数
result = get_cursor_token_via_api()
if result:
    print(f"获取成功: {result}")
else:
    print("获取失败")


# https://authenticator.cursor.sh/?client_id=client_01GS6W3C96KW4WRS6Z93JCE2RJ&redirect_uri=https%3A%2F%2Fcursor.com%2Fapi%2Fauth%2Fcallback&response_type=code&state=%257B%2522returnTo%2522%253A%2522https%253A%252F%252Fwww.cursor.com%252Fcn%252FloginDeepControl%253Fchallenge%253DeJWcV1_R4KmjQO3Aabs2MiWBjOYI19IYMco0nYyfi1E%2526uuid%253D0f6d2037-7940-426c-b79d-556611a94908%2526mode%253Dlogin%2522%257D&authorization_session_id=01JR7H2ZMPGYEMSCVFR2RVN25D