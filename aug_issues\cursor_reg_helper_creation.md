# Cursor Register Helper 脚本创建任务

## 任务概述
基于 aug_reg_helper.js 的框架和 UI，创建适用于 cursor.sh 和 cursor.com 域名的注册助手脚本。

## 上下文
- **基础文件**: aug_reg_helper.js (Augment注册助手)
- **参考文件**: cursor_reg_longtoken.py (邮箱生成和验证码获取逻辑)
- **目标**: 创建 cursor_reg_helper.js

## 执行计划

### 1. 文件创建和基础设置 ✅
- 创建 cursor_reg_helper.js 文件
- 修改 UserScript 头部信息
  - 名称: "Cursor Register Helper"
  - 匹配域名: cursor.sh 和 cursor.com
  - 添加必要的 @connect 权限

### 2. 邮箱生成逻辑替换 ✅
- 实现 cursor_reg_longtoken.py 中的邮箱生成算法
  - 生成 10-15 位随机前缀
  - 使用小写字母和数字组合
  - 固定后缀: @uvhivmqp.me
- 移除原有的域名选择下拉框

### 3. UI 组件更新 ✅
- 标题更改为 "Cursor 注册助手"
- 按钮文本修改:
  - "前往订阅页面" → "前往设置页面"
- 保持其他 UI 元素和样式不变

### 4. 验证码获取逻辑适配 ✅
- 保留 MailCX 类的基本结构
- 更新验证码提取正则表达式
- 适配 cursor 邮件格式的验证码匹配

### 5. DOM 选择器适配 ✅
- 保持现有的多重选择器策略
- 适配 cursor 网站的表单结构
- 保持容错机制和自动填充逻辑

### 6. 按钮功能实现 ✅
- **生成并填充邮箱**: 生成邮箱和密码，自动填充表单
- **获取验证码**: 从邮箱获取验证码
- **填充验证码**: 自动填充验证码到表单
- **清空邮箱**: 清空临时邮箱
- **清除数据**: 清除所有保存的数据
- **前往设置页面**: 打开 cursor 设置页面获取 token

### 7. 功能特性 ✅
- 自动填充机制（多次尝试）
- 数据持久化存储
- 错误处理和状态显示
- 密码生成功能
- 邮箱清理功能

## 主要功能

### 邮箱生成
```javascript
function generateRandomEmail() {
    const prefixLength = Math.floor(Math.random() * 6) + 10; // 10-15位
    const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let prefix = '';
    
    for (let i = 0; i < prefixLength; i++) {
        prefix += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    
    return prefix + '<EMAIL>';
}
```

### 验证码提取
```javascript
function extractVerificationCode(emailContent) {
    // 尝试匹配 "code is XXXXXX" 格式
    let codeRegex = /code is (\d{6})/i;
    let match = emailContent.match(codeRegex);
    if (match) return match[1];
    
    // 尝试直接匹配6位数字
    const numbers = emailContent.match(/\b\d{6}\b/g);
    if (numbers && numbers.length > 0) {
        return numbers[numbers.length - 1];
    }
    
    return null;
}
```

## 技术实现

### 支持的域名
- *.cursor.sh/*
- *.cursor.com/*

### 邮箱服务
- 使用 uvhivmqp.me 后缀
- 通过 MailCX API 获取验证码

### 自动填充策略
1. 常规 DOM 选择器匹配
2. 直接 DOM 操作填充
3. 多次重试机制
4. 容错处理

## 完成状态
✅ 任务已完成

## 输出文件
- `cursor_reg_helper.js` - 完整的 Cursor 注册助手脚本

## 使用说明
1. 在 Tampermonkey 中安装脚本
2. 访问 cursor.sh 或 cursor.com 注册页面
3. 使用右上角的助手面板进行注册辅助
4. 完成注册后可通过"前往设置页面"获取 token
