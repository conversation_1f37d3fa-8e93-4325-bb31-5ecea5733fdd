# -*- mode: python ; coding: utf-8 -*-
import os
import customtkinter
import sys

block_cipher = None

# 获取 customtkinter 路径
ctk_path = os.path.dirname(customtkinter.__file__)

a = Analysis(
    ['crazy_cursor_gui.py'],
    pathex=[],
    binaries=[],
    datas=[
        (ctk_path, 'customtkinter'),
        (os.path.join(ctk_path, 'assets'), 'customtkinter/assets'),
    ],
    hiddenimports=[
        'customtkinter',
        'requests',
        'psutil',
        'logging',
        'json',
        'base64',
        'uuid',
        'hashlib',
        'sqlite3',
        'webbrowser',
        'queue',
        'threading',
        'winreg',
        'encodings.utf_8',
        'encodings.ascii',
        'encodings.idna',
        'charset_normalizer.md__mypyc',
        'PIL._tkinter_finder',
        'tkinter',
        '_tkinter',
        'PIL',
        'PIL._imaging',
        'PIL._tkinter_finder',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['matplotlib', 'pandas', 'scipy', 'notebook'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Crazy Cursor',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None
) 