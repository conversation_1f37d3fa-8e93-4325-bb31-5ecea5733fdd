import requests
import json
from typing import Optional, Dict, List
from urllib.parse import quote
import urllib3

# 禁用SSL验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class MailCXError(Exception):
    """自定义异常类用于处理MailCX相关错误"""
    pass

class MailCX:
    """MailCX API客户端类"""
    
    BASE_URL = "https://api.mail.cx/api/v1"
    
    def __init__(self):
        """初始化邮箱客户端"""
        self.session = requests.Session()
        
        # 完全禁用代理
        self.session.trust_env = False  # 禁用环境变量中的代理设置
        
        # 配置SSL
        self.session.verify = False  # 禁用SSL验证
        
        # 设置基本请求参数
        self.session.timeout = 30
        
        self.token = None
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict:
        """发送HTTP请求的通用方法"""
        url = f"{self.BASE_URL}{endpoint}"
        
        # 添加认证header
        headers = kwargs.get('headers', {})
        headers['accept'] = 'application/json'
        headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        if self.token:
            headers['Authorization'] = f'Bearer {self.token}'
        kwargs['headers'] = headers
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            
            if response.content:  # 检查是否有响应内容
                try:
                    return response.json()
                except json.JSONDecodeError:
                    # 如果不是JSON，返回文本内容
                    return response.text
            return {}
            
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {str(e)}")
            print(f"URL: {url}")
            print(f"Method: {method}")
            print(f"Headers: {headers}")
            print(f"Response: {getattr(e.response, 'text', '')}")
            raise MailCXError(f"请求失败: {str(e)}")
    
    def authorize(self) -> None:
        """获取认证token"""
        headers = {
            'accept': 'application/json',
            'Authorization': 'Bearer undefined'
        }
        try:
            response = self._make_request('POST', '/auth/authorize_token', headers=headers)
            # response直接就是token字符串
            if isinstance(response, str):
                self.token = response.strip('"')  # 移除可能的引号
            else:
                raise MailCXError("无法获取token")
        except Exception as e:
            print(f"认证失败: {str(e)}")
            raise
    
    def get_mailbox(self, email: str) -> List[Dict]:
        """获取邮箱信息
        Args:
            email: 完整邮箱地址，如 '<EMAIL>'
        """
        if not self.token:
            self.authorize()
        encoded_email = quote(email)
        return self._make_request('GET', f'/mailbox/{encoded_email}')
    
    def get_message(self, email: str, message_id: str) -> Dict:
        """获取指定邮件内容
        Args:
            email: 完整邮箱地址
            message_id: 邮件ID，如 '20250301T025351-8926'
        """
        if not self.token:
            self.authorize()
        encoded_email = quote(email)
        return self._make_request('GET', f'/mailbox/{encoded_email}/{message_id}')
    
    def delete_message(self, email: str, message_id: str) -> Dict:
        """删除指定邮件
        Args:
            email: 完整邮箱地址
            message_id: 邮件ID
        """
        if not self.token:
            self.authorize()
        encoded_email = quote(email)
        return self._make_request('DELETE', f'/mailbox/{encoded_email}/{message_id}')

def main():
    """使用示例"""
    # 完整邮箱地址
    email = "<EMAIL>"
    
    # 创建客户端实例
    mail = MailCX()
    
    try:
        # 获取认证token
        mail.authorize()
        print("认证成功，token:", mail.token)
        
        # 获取邮件列表
        messages = mail.get_mailbox(email)
        print(f"原始响应: {messages}")
        
        if isinstance(messages, list):
            print(f"找到 {len(messages)} 封邮件")
            
            # 处理每封邮件
            for msg in messages:
                # 获取邮件详情
                message = mail.get_message(email, msg['id'])
                print(f"邮件主题: {message.get('subject')}")
                print(f"邮件内容: {message.get('text')}")
                
                # 删除邮件
                mail.delete_message(email, msg['id'])
                print(f"已删除邮件 ID: {msg['id']}")
        else:
            print("未找到邮件或响应格式不正确")
                
    except MailCXError as e:
        print(f"错误: {str(e)}")
    except Exception as e:
        print(f"未知错误: {str(e)}")

if __name__ == "__main__":
    main()
