from math import log
import random
import string
import time
import os
import csv
import argparse
import uuid
import base64
import hashlib
import secrets
import requests
import logging
import winreg
from datetime import datetime
from DrissionPage import Chromium, ChromiumOptions
from colorama import init, Fore, Style
import re
from mailcx import MailCX
from typing import Optional, Tuple

# 初始化colorama
init()

def setup_argparse():
    """设置命令行参数"""
    parser = argparse.ArgumentParser(description='Cursor账号注册工具')
    parser.add_argument('-n', '--number', type=int, default=1,
                      help='要注册的账号数量 (默认: 22)')
    parser.add_argument('--save-tokens', action='store_true',
                      help='将有效token保存到单独的文件')
    parser.add_argument('--skip-null-tokens', action='store_true',
                      help='跳过没有有效token的账号')
    parser.add_argument('--get-long-token', action='store_true', default=True,
                      help='获取长效token（默认开启）')
    return parser.parse_args()

def log_info(message):
    """输出信息日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{Fore.GREEN}[{timestamp}] INFO: {message}{Style.RESET_ALL}")
    logging.info(message)

def log_error(message):
    """输出错误日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{Fore.RED}[{timestamp}] ERROR: {message}{Style.RESET_ALL}")
    logging.error(message)

def log_warning(message):
    """输出警告日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{Fore.YELLOW}[{timestamp}] WARNING: {message}{Style.RESET_ALL}")
    logging.warning(message)

def get_proxy():
    """从Windows注册表获取代理设置"""
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Internet Settings") as key:
            proxy_enable, _ = winreg.QueryValueEx(key, "ProxyEnable")
            proxy_server, _ = winreg.QueryValueEx(key, "ProxyServer")
            
            if proxy_enable and proxy_server:
                proxy_parts = proxy_server.split(":")
                if len(proxy_parts) == 2:
                    return {"http": f"http://{proxy_server}", "https": f"http://{proxy_server}"}
    except WindowsError:
        pass
    return {"http": None, "https": None}

def get_cursor_session_token(tab, max_attempts: int = 3, retry_interval: int = 2) -> Optional[Tuple[str, str]]:
    """
    获取Cursor会话token
    
    Args:
        tab: 浏览器标签页对象
        max_attempts: 最大尝试次数
        retry_interval: 重试间隔(秒)
        
    Returns:
        Tuple[str, str] | None: 成功返回(userId, accessToken)元组，失败返回None
    """
    log_info("开始获取长效会话令牌")
    
    # 参数验证
    if not tab:
        log_error("浏览器标签页对象不能为空")
        return None
    
    # 首先尝试使用UUID深度登录方式
    log_info("尝试使用深度登录方式获取token")
    
    def _generate_pkce_pair():
        """生成PKCE验证对"""
        code_verifier = secrets.token_urlsafe(43)
        code_challenge_digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        code_challenge = base64.urlsafe_b64encode(code_challenge_digest).decode('utf-8').rstrip('=')    
        return code_verifier, code_challenge
    
    attempts = 0
    while attempts < max_attempts:
        try:
            verifier, challenge = _generate_pkce_pair()
            id = uuid.uuid4()
            client_login_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={id}&mode=login"
            
            log_info(f"访问深度登录URL: {client_login_url}")
            tab.get(client_login_url)
            
            if tab.ele("xpath=//span[contains(text(), 'Yes, Log In')]", timeout=5):
                log_info("点击确认登录按钮")
                tab.ele("xpath=//span[contains(text(), 'Yes, Log In')]").click()
                time.sleep(1.5)
                
                auth_poll_url = f"https://api2.cursor.sh/auth/poll?uuid={id}&verifier={verifier}"
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Accept": "*/*"
                }
                
                log_info(f"轮询认证状态: {auth_poll_url}")
                response = requests.get(auth_poll_url, headers=headers, proxies=get_proxy(), timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    accessToken = data.get("accessToken", None)
                    authId = data.get("authId", "")
                    
                    if accessToken:
                        userId = ""
                        if len(authId.split("|")) > 1:
                            userId = authId.split("|")[1]
                        
                        log_info("成功获取账号token和userId")
                        return userId, accessToken
                else:
                    log_error(f"API请求失败，状态码: {response.status_code}")
            else:
                log_warning("未找到登录确认按钮")
                
            attempts += 1
            if attempts < max_attempts:
                wait_time = retry_interval * attempts  # 逐步增加等待时间
                log_warning(f"第 {attempts} 次尝试未获取到token，{wait_time}秒后重试...")
                time.sleep(wait_time)
                
        except Exception as e:
            log_error(f"深度登录获取token失败: {str(e)}")
            attempts += 1
            if attempts < max_attempts:
                wait_time = retry_interval * attempts
                log_warning(f"将在 {wait_time} 秒后重试...")
                time.sleep(wait_time)
    
    log_error(f"经过 {max_attempts} 次尝试后仍未获取到token")
    return None

def generate_password(length=12):
    """生成随机密码"""
    return "".join(random.choices(string.ascii_letters + string.digits, k=length))

def generate_email():
    """生成随机前缀(长度至少10位) + @mailbox.fuclaude.me后缀的邮箱"""
    # 确保前缀长度至少为10位
    prefix_length = random.randint(10, 15)
    prefix_chars = random.choices(string.ascii_lowercase + string.digits, k=prefix_length)
    
    prefix = ''.join(prefix_chars)
    return f"{prefix}<EMAIL>"

def get_token_from_cookies(cookies):
    """从cookies中提取WorkosCursorSessionToken"""
    return cookies.get('WorkosCursorSessionToken', 'null')

def setup_output_files(args):
    """设置输出文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    output_dir = os.path.join(current_dir, 'output')
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_file = os.path.join(output_dir, f'cursor_accounts_{timestamp}.csv')
    token_file = os.path.join(output_dir, f'cursor_tokens_{timestamp}.txt')
    
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['username', 'password', 'cookie', 'token', 'userId', 'longToken'])
    
    log_info(f"CSV输出文件: {csv_file}")
    if args.save_tokens:
        log_info(f"Token输出文件: {token_file}")
    
    return csv_file, token_file

def handle_first_turnstile(tab):
    """处理 Turnstile 验证"""
    log_info("准备处理验证")
    max_attempts = 5 
    attempt = 0
    
    try:
        while attempt < max_attempts:
            attempt += 1
            log_info(f"第 {attempt} 次尝试处理验证")
            try:
                iframe = tab.ele('xpath:/html/body/div[1]/div/div[1]/div/div',timeout=2)
                if not iframe:
                    if tab.ele('@name=email',timeout=2):
                        log_info("已进入Cursor登录页面")   
                        return True                
                    log_info("未找到验证框,等待后重试...")
                    time.sleep(2)
                    continue
                    
                challenge = (iframe.shadow_root
                            .ele("tag:iframe")
                            .ele("tag:body")
                            .sr("tag:input"))
                           
                if challenge:
                    log_info("找到验证按钮,点击验证...")
                    challenge.click()
                    time.sleep(3)
                else:
                    log_info("未找到验证按钮,重试...")
                    
            except Exception as e:
                log_info(f"当前尝试出错: {e}")
                time.sleep(2)
                continue

            if tab.ele('@name=email',timeout=2) or \
               tab.ele('@id=identifierId',timeout=2):
                log_info("验证已通过")   
                return True
            time.sleep(2)
            
        log_info(f"验证处理超过最大尝试次数({max_attempts})")
        return False
            
    except Exception as e:
        log_error(f"验证处理出错: {e}")
        return False

def handle_turnstile(tab):
    """处理 Turnstile 验证"""
    log_info("准备处理验证")
    max_attempts = 5 
    attempt = 0
    
    try:
        while attempt < max_attempts:
            attempt += 1
            log_info(f"第 {attempt} 次尝试处理验证")
            
            try:
                iframe = tab.ele('@id=cf-turnstile',timeout=2)
                if not iframe:
                    if tab.ele('@name=password',timeout=1):
                        log_info("已进入密码输入页面")
                        return True 
                    if tab.ele('@data-index=0',timeout=1):
                        log_info("已进入邮箱验证页面")
                        return True
                    if tab.ele('Account Settings',timeout=1):
                        log_info("已登录账号")
                        return True                   
                    log_info("未找到验证框,等待后重试...")
                    time.sleep(2)
                    continue
                    
                challenge = (iframe.child()
                           .shadow_root
                           .ele("tag:iframe")
                           .ele("tag:body")
                           .sr("tag:input"))
                           
                if challenge:
                    log_info("找到验证按钮,点击验证...")
                    challenge.click()
                    time.sleep(3)
                else:
                    log_info("未找到验证按钮,重试...")
                    
            except Exception as e:
                time.sleep(2)
                continue

            if tab.ele('@name=password',timeout=1) or \
               tab.ele('@data-index=0',timeout=1) or \
               tab.ele('Account Settings',timeout=1):
                log_info("验证已通过")   
                return True
            time.sleep(2)
            
        log_warning(f"验证处理超过最大尝试次数({max_attempts})，跳过当前账号注册")
        return False
            
    except Exception as e:
        log_error(f"验证处理出错: {e}")
        return False

def wait_for_verification_code(mail_client, cx_email, max_wait_time=30, check_interval=5):
    """等待并获取验证码邮件"""
    
    log_info(f"等待接收验证码邮件...")
    start_time = time.time()

    while time.time() - start_time < max_wait_time:
        try:
            # 获取邮件列表
            messages = mail_client.get_mailbox(cx_email)
            
            if isinstance(messages, list) and messages:
                # 只处理最新的邮件（假设邮件按时间排序，最新的在前面）
                msg = messages[-1]
                
                # 获取邮件详情
                message = mail_client.get_message(cx_email, msg['id'])
                
                # 获取邮件内容
                body = message.get('body', {})
                if isinstance(body, dict) and 'text' in body:
                    content = body['text']
                    
                    # 尝试匹配验证码
                    code_match = re.search(r'code is (\d{6})', content, re.IGNORECASE)
                    if not code_match:
                        # 尝试直接匹配6位数字
                        numbers = re.findall(r'\b\d{6}\b', content)
                        if numbers:
                            verify_code = numbers[-1]  # 取最后一个匹配的6位数字
                            log_info(f"获取到验证码: {verify_code}")
                            
                            # 删除已处理的邮件
                            try:
                                mail_client.delete_message(cx_email, msg['id'])
                                log_info(f"已删除邮件 ID: {msg['id']}")
                            except Exception as e:
                                log_warning(f"删除邮件失败: {str(e)}")
                                
                            return verify_code
                    else:
                        verify_code = code_match.group(1)
                        log_info(f"获取到验证码: {verify_code}")
                        
                        # 删除已处理的邮件
                        try:
                            mail_client.delete_message(cx_email, msg['id'])
                            log_info(f"已删除邮件 ID: {msg['id']}")
                        except Exception as e:
                            log_warning(f"删除邮件失败: {str(e)}")
                            
                        return verify_code
            
            # 如果没有找到验证码，等待一段时间后重试
            time.sleep(check_interval)
            
        except Exception as e:
            log_error(f"获取邮件时出错: {str(e)}")
            time.sleep(check_interval)
    
    raise Exception("等待验证码超时")

def register_account(get_long_token=True):
    """注册一个新账户"""
    # 初始化浏览器
    log_info("初始化浏览器...")
    co = ChromiumOptions()
    co.set_browser_path(r'C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1067\chrome-win\chrome.exe')
    co.add_extension("turnstilePatch")
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.69 Safari/537.36')   
    co.set_pref('credentials_enable_service', False)
    co.set_argument('--hide-crash-restore-bubble')
    co.auto_port()
    co.incognito()
    browser = Chromium(co)
    
    # 打开注册页面
    log_info("打开注册页面...")
    reg_tab = browser.new_tab()
    reg_tab.run_js("try { turnstile.reset() } catch(e) { }")
    reg_tab.get("https://authenticator.cursor.sh/sign-up")

    # 初始化邮箱客户端
    cx_email = "<EMAIL>"

    mail_client = MailCX()
    try:
        mail_client.authorize()
        log_info("邮箱API认证成功")
    except Exception as e:
        log_error(f"邮箱API认证失败: {str(e)}")
        browser.quit()
        return None
    
    # 先清空邮箱中的所有邮件
    try:
        log_info(f"清空cx接收邮箱中的所有邮件...")
        mail_client.delete_mailbox(cx_email)
        log_info(f"邮箱清空成功")
    except Exception as e:
        log_warning(f"清空邮箱失败: {str(e)}")

    try: 
        # 生成邮箱和密码
        email = generate_email()
        password = generate_password()
        log_info(f"生成邮箱: {email}")
        log_info(f"生成密码: {password}")

        attempt = 0
        while attempt < 5:
            attempt += 1
            log_info(f"第 {attempt} 次检测邮箱输入框")
            try:
                if reg_tab.ele('@type=email'):
                    log_info("邮箱输入框已加载")
                    break
                #         # 处理验证
                if not handle_first_turnstile(reg_tab):
                    log_warning("验证处理失败，中止当前账号注册")
                    return None
            except Exception as e:
                log_error(f"邮箱输入框未加载{e}")
            time.sleep(3)

        # 输入邮箱
        log_info("输入邮箱地址...")
        time.sleep(2)
        email_input = reg_tab.ele("@type=email")
        email_input.input(email)
        time.sleep(1)
        reg_tab.ele('@text()=Continue').click()

        # 处理验证
        if not handle_turnstile(reg_tab):
            log_warning("验证处理失败，中止当前账号注册")
            return None
        time.sleep(3)
   
        # 输入密码
        log_info("输入密码...")
        password_input = reg_tab.ele("@type=password")
        password_input.input(password)
        time.sleep(1)
        reg_tab.ele('@text()=Continue').click()
        time.sleep(2)
        
        # 处理验证
        if not handle_turnstile(reg_tab):
            log_warning("验证处理失败，中止当前账号注册")
            return None

        # 等待并获取验证码
        verify_code = wait_for_verification_code(mail_client, cx_email)
        
        # 切换回注册页面输入验证码
        log_info("输入验证码...")
        for digit in verify_code:
            reg_tab.actions.key_down(str(digit))
            time.sleep(0.1)
            reg_tab.actions.key_up(str(digit))
        
        if not handle_turnstile(reg_tab):
            log_warning("验证处理失败，中止当前账号注册")
            return None
  
        attempt = 0
        while attempt < 5:
            attempt += 1
            log_info(f"第 {attempt} 次检测登录状态")
            try:
                if reg_tab.ele('Account Settings'):
                    log_info("账号登录成功")
                    break
            except Exception as e:
                log_error(f"账号登录失败{e}")
            time.sleep(3)

        # 等待注册完成并获取token
        log_info("等待注册完成并跳转到设置页面...")
        
        # 访问settings页面获取token
        reg_tab.get("https://www.cursor.com/settings")
        time.sleep(3)
        
        # 获取cookies和token
        cookies = reg_tab.cookies().as_dict()
        token = get_token_from_cookies(cookies)
        
        if token == 'null':
            log_warning("未能获取到有效token,尝试刷新页面...")
            reg_tab.refresh()
            time.sleep(3)
            cookies = reg_tab.cookies().as_dict()
            token = get_token_from_cookies(cookies)
        
        log_info("注册完成，获取到账号信息")
        
        userId = None
        longToken = None
        
        # 如果需要获取长效token
        if get_long_token and token != 'null':
            log_info("开始获取长效token...")
            token_result = get_cursor_session_token(reg_tab)
            if token_result:
                userId, longToken = token_result
                log_info(f"成功获取长效token, userId: {userId}")
            else:
                log_warning("获取长效token失败")
        
        return {
            'username': email,
            'password': password,
            'cookie': str(cookies),
            'token': token,
            'userId': userId if userId else '',
            'longToken': longToken if longToken else ''
        }
    finally:
        browser.quit()
        # pass

def main():
    args = setup_argparse()
    
    # 如果命令行没有指定get_long_token，默认设为True
    if not hasattr(args, 'get_long_token') or args.get_long_token is None:
        args.get_long_token = True
    
    # # 或者直接在命令行参数中设置默认值
    # parser.add_argument('--get-long-token', action='store_true', default=True,
    #                   help='获取长效token（默认开启）')
    
    csv_file, token_file = setup_output_files(args)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("cursor_reg_longtoken.log"),
            logging.StreamHandler()
        ]
    )
    
    log_info(f"开始注册 {args.number} 个账号...")
    successful_registrations = 0
    
    for i in range(args.number):
        try:
            log_info(f"\n=== 开始注册第 {i+1}/{args.number} 个账号 ===")
            account_info = register_account(get_long_token=args.get_long_token)
            
            if not account_info:
                log_error("账号注册失败，跳过")
                continue
                
            # 检查token是否为null
            if args.skip_null_tokens and account_info['token'] == 'null':
                log_warning(f"跳过无效token的账号: {account_info['username']}")
                continue
            
            # 写入CSV文件
            with open(csv_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    account_info['username'],
                    account_info['password'],
                    account_info['cookie'],
                    account_info['token'],
                    account_info['userId'],
                    account_info['longToken']
                ])
            
            # 如果需要，将token写入单独的文件
            if args.save_tokens and account_info['token'] != 'null':
                with open(token_file, 'a', encoding='utf-8') as f:
                    if account_info['longToken']:
                        f.write(f"{account_info['longToken']}\n")
                    else:
                        f.write(f"{account_info['token']}\n")
            
            successful_registrations += 1
            log_info(f"账号注册成功: {account_info['username']}")
            if account_info['longToken']:
                log_info(f"长效Token已获取: {account_info['longToken']}")
            else:
                log_info(f"Token: {account_info['token']}")
            
        except Exception as e:
            log_error(f"注册失败: {str(e)}")
            continue
    
    log_info("\n=== 注册完成 ===")
    log_info(f"成功注册账号: {successful_registrations}/{args.number}")
    log_info(f"账号信息已保存到: {csv_file}")
    if args.save_tokens:
        log_info(f"Token已保存到: {token_file}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log_warning("\n程序被用户中断")
    except Exception as e:
        log_error(f"程序异常退出: {str(e)}") 