import logging
import time
import uuid
import base64
import hashlib
import secrets
import requests
import json
import os
import re
from typing import Optional, <PERSON><PERSON>
from DrissionPage import Chromium, ChromiumOptions
import winreg

def get_proxy():
    try:
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Internet Settings") as key:
            proxy_enable, _ = winreg.QueryValueEx(key, "ProxyEnable")
            proxy_server, _ = winreg.QueryValueEx(key, "ProxyServer")
            
            if proxy_enable and proxy_server:
                proxy_parts = proxy_server.split(":")
                if len(proxy_parts) == 2:
                    return {"http": f"http://{proxy_server}", "https": f"http://{proxy_server}"}
    except WindowsError:
        pass
    return {"http": None, "https": None}

def get_cursor_session_token(tab, max_attempts: int = 3, retry_interval: int = 2) -> Optional[Tuple[str, str]]:
    """
    获取Cursor会话token
    
    Args:
        tab: 浏览器标签页对象
        max_attempts: 最大尝试次数
        retry_interval: 重试间隔(秒)
        
    Returns:
        Tuple[str, str] | None: 成功返回(userId, accessToken)元组，失败返回None
    """
    logging.info("开始获取会话令牌")
    
    # 参数验证
    if not tab:
        logging.error("浏览器标签页对象不能为空")
        return None
    
    # 首先尝试使用UUID深度登录方式
    logging.info("尝试使用深度登录方式获取token")
    
    def _generate_pkce_pair():
        """生成PKCE验证对"""
        code_verifier = secrets.token_urlsafe(43)
        code_challenge_digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        code_challenge = base64.urlsafe_b64encode(code_challenge_digest).decode('utf-8').rstrip('=')    
        return code_verifier, code_challenge
    
    attempts = 0
    while attempts < max_attempts:
        try:
            verifier, challenge = _generate_pkce_pair()
            id = uuid.uuid4()
            client_login_url = f"https://www.cursor.com/cn/loginDeepControl?challenge={challenge}&uuid={id}&mode=login"
            
            logging.info(f"访问深度登录URL: {client_login_url}")
            tab.get(client_login_url)
            
            if tab.ele("xpath=//span[contains(text(), 'Yes, Log In')]", timeout=5):
                logging.info("点击确认登录按钮")
                tab.ele("xpath=//span[contains(text(), 'Yes, Log In')]").click()
                time.sleep(1.5)
                
                auth_poll_url = f"https://api2.cursor.sh/auth/poll?uuid={id}&verifier={verifier}"
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Accept": "*/*"
                }
                
                logging.info(f"轮询认证状态: {auth_poll_url}")
                response = requests.get(auth_poll_url, headers=headers, proxies=get_proxy(), timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    accessToken = data.get("accessToken", None)
                    authId = data.get("authId", "")
                    
                    if accessToken:
                        userId = ""
                        if len(authId.split("|")) > 1:
                            userId = authId.split("|")[1]
                        
                        logging.info("成功获取账号token和userId")
                        return userId, accessToken
                else:
                    logging.error(f"API请求失败，状态码: {response.status_code}")
            else:
                logging.warning("未找到登录确认按钮")
                
            attempts += 1
            if attempts < max_attempts:
                wait_time = retry_interval * attempts  # 逐步增加等待时间
                logging.warning(f"第 {attempts} 次尝试未获取到token，{wait_time}秒后重试...")
                time.sleep(wait_time)
                
        except Exception as e:
            logging.error(f"深度登录获取token失败: {str(e)}")
            attempts += 1
            if attempts < max_attempts:
                wait_time = retry_interval * attempts
                logging.warning(f"将在 {wait_time} 秒后重试...")
                time.sleep(wait_time)
    
    logging.error(f"经过 {max_attempts} 次尝试后仍未获取到token")
    return None

def save_token_to_file(user_id: str, access_token: str, file_path: str = "cursor_token.json") -> bool:
    """
    将用户ID和访问令牌保存到文件
    
    Args:
        user_id: 用户ID
        access_token: 访问令牌
        file_path: 保存的文件路径
        
    Returns:
        bool: 保存成功返回True，失败返回False
    """
    try:
        # 解析JWT部分，获取过期时间
        jwt_parts = access_token.split('.')
        if len(jwt_parts) >= 2:
            # Base64解码
            padding = '=' * (4 - len(jwt_parts[1]) % 4)
            payload = base64.b64decode(jwt_parts[1] + padding)
            payload_data = json.loads(payload.decode('utf-8'))
            exp_timestamp = payload_data.get('exp', 0)
            exp_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(exp_timestamp))
        else:
            exp_time = "未知"
        
        # 创建token数据
        token_data = {
            "user_id": user_id,
            "access_token": access_token,
            "created_at": time.strftime('%Y-%m-%d %H:%M:%S'),
            "expires_at": exp_time,
            "is_valid": True
        }
        
        # 保存到文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(token_data, f, ensure_ascii=False, indent=2)
            
        print(f"Token已成功保存到文件: {os.path.abspath(file_path)}")
        print(f"过期时间: {exp_time}")
        return True
    except Exception as e:
        print(f"保存token到文件失败: {str(e)}")
        return False

def save_long_token_to_file(user_id: str, access_token: str, file_path: str = "long_token.txt"):
    """
    将长效token保存到文件
    
    Args:
        user_id: 用户ID
        access_token: 访问令牌
        file_path: 保存的文件路径
    
    Returns:
        bool: 保存成功返回True，失败返回False
    """
    try:
        # 格式化为长效token格式：user_ID::JWT
        long_token = f"user_{user_id}::{access_token}"
        
        # 添加到文件末尾
        with open(file_path, 'a', encoding='utf-8') as f:
            f.write(long_token + '\n')
            
        print(f"长效token已保存到文件: {os.path.abspath(file_path)}")
        return True
    except Exception as e:
        print(f"保存长效token到文件失败: {str(e)}")
        return False

def load_token_from_file(file_path: str = "cursor_token.json") -> Optional[dict]:
    """
    从文件加载token信息
    
    Args:
        file_path: token文件路径
        
    Returns:
        dict: 成功返回token信息字典，失败返回None
    """
    try:
        if not os.path.exists(file_path):
            print(f"Token文件不存在: {file_path}")
            return None
            
        with open(file_path, 'r', encoding='utf-8') as f:
            token_data = json.load(f)
            
        # 检查token是否已过期
        if 'expires_at' in token_data:
            try:
                # 将过期时间字符串转换为时间戳
                expires_str = token_data['expires_at']
                expires_time = time.strptime(expires_str, '%Y-%m-%d %H:%M:%S')
                expires_timestamp = time.mktime(expires_time)
                
                # 比较当前时间和过期时间
                current_timestamp = time.time()
                if current_timestamp > expires_timestamp:
                    print(f"Token已过期(过期时间: {expires_str})")
                    token_data['is_valid'] = False
                else:
                    remaining_seconds = int(expires_timestamp - current_timestamp)
                    remaining_days = remaining_seconds // (24 * 3600)
                    remaining_hours = (remaining_seconds % (24 * 3600)) // 3600
                    
                    print(f"Token有效，剩余时间: {remaining_days}天{remaining_hours}小时")
            except Exception as e:
                print(f"检查token过期时间时出错: {str(e)}")
        
        return token_data
    except Exception as e:
        print(f"从文件加载token失败: {str(e)}")
        return None

def extract_user_id_from_jwt(token: str) -> str:
    """从JWT中提取用户ID"""
    try:
        payload = json.loads(base64.b64decode(token.split('.')[1] + '==').decode('utf-8'))
        match = re.search(r'auth0\|(.+)', payload.get('sub', ''))
        return match.group(1) if match else ''
    except Exception as e:
        print(f"提取用户ID失败: {str(e)}")
        return ''

def read_tokens_from_file(file_path: str = "short_token.txt") -> list:
    """
    从文件中读取多个token
    
    Args:
        file_path: token文件路径
    
    Returns:
        list: token列表
    """
    tokens = []
    try:
        if not os.path.exists(file_path):
            print(f"Token文件不存在: {file_path}")
            return tokens
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    tokens.append(line)
        
        print(f"从{file_path}读取了{len(tokens)}个token")
        return tokens
    except Exception as e:
        print(f"读取token文件失败: {str(e)}")
        return tokens

if __name__ == "__main__":
    try:
        # 从文件读取短token
        short_tokens = read_tokens_from_file()
        if not short_tokens:
            print("短token文件为空或不存在，请检查short_token.txt文件")
            exit(1)
        
        processed_count = 0
        failed_count = 0
        
        # 初始化浏览器
        co = ChromiumOptions()
        co.set_browser_path(r'C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1067\chrome-win\chrome.exe')
        co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.69 Safari/537.36')   
        co.set_pref('credentials_enable_service', False)
        co.set_argument('--hide-crash-restore-bubble')
        # co.headless()
        co.auto_port()
        
        # 创建浏览器
        browser = Chromium(co)
        browser_tab = browser.latest_tab
        
        # 处理每个短token
        for short_token in short_tokens:
            print(f"\n处理token: {short_token[:10]}...")
            
            # 从token中提取用户ID
            user_id = extract_user_id_from_jwt(short_token)
            if not user_id:
                print("无法从token中提取用户ID，跳过")
                failed_count += 1
                continue
            
            print(f"提取到用户ID: {user_id}")
            
            # 合成cookie的value
            cookie_value = f"user_{user_id}::{short_token}"
            
            # 设置cookie
            cookie = {
                'name': 'WorkosCursorSessionToken',
                'value': cookie_value,
                'domain': '.cursor.com',
                'path': '/'
            }
            browser_tab.set.cookies(cookie)
            
            # 访问网站获取长效token
            browser_tab.get('https://www.cursor.com')
            time.sleep(2)  # 等待页面加载
            
            # 获取长效token
            result = get_cursor_session_token(browser_tab)
            if result:
                user_id, access_token = result
                print(f"成功获取长效token")
                print(f"用户ID: {user_id}")
                print(f"访问令牌: {access_token[:10]}...")
                
                # 保存长效token到文件
                save_long_token_to_file(user_id, access_token)
                
                # 同时保存详细信息到JSON
                save_token_to_file(user_id, access_token)
                
                processed_count += 1
            else:
                print("获取长效token失败")
                failed_count += 1
        
        print(f"\n处理完成! 成功:{processed_count}, 失败:{failed_count}")
        
        # 关闭浏览器
        browser.quit()
            
    except Exception as e:
        print(f"运行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()