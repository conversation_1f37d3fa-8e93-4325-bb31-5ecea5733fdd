import customtkinter as ctk
import threading
import webbrowser
import queue
import logging
from typing import Dict, Optional
import sys
import traceback


# 导入需要的类
from crazy_cursor_ctk import (
    CursorAuthManager,
    UsageManager,
    FilePathManager,
    Utils,
    CursorPatcher,
    CursorManager,
    TokenManager,
    logger,
    generate_ids,
    TokenData,
    UpdateManager
)

# 日志处理类
class QueueHandler(logging.Handler):
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(self.format(record))

class CursorGUI:
    def __init__(self):
        try:
            # 设置Win11风格主题
            ctk.set_appearance_mode("light")
            ctk.set_default_color_theme("blue")
            
            # 创建主窗口
            self.root = ctk.CTk()
            self.root.title("Crazy Cursor")
            self.root.geometry("800x500")
            
            # 设置主窗口居中显示
            self.center_window(self.root)
            
            logger.info("窗口创建成功")
            
            # 主框架
            self.main_frame = ctk.CTkFrame(
                self.root,
                fg_color="#ffffff",
                corner_radius=8
            )
            self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
            
            # 创建左右分栏
            self.left_frame = ctk.CTkFrame(
                self.main_frame,
                fg_color="#f5f5f5",
                corner_radius=8,
                width=300
            )
            self.left_frame.pack(side="left", fill="both", padx=(10, 5), pady=10)
            self.left_frame.pack_propagate(False)
            
            self.right_frame = ctk.CTkFrame(
                self.main_frame,
                fg_color="transparent"
            )
            self.right_frame.pack(side="left", fill="both", expand=True, padx=(5, 10), pady=10)
            
            # 获取系统字体
            self.system_font = self.get_system_font()
            
            # 标题
            self.title_label = ctk.CTkLabel(
                self.left_frame, 
                text="Crazy Cursor",
                font=(self.system_font, 20, "bold"),
                text_color="#333333"
            )
            self.title_label.pack(pady=(20, 20))
            
            # 账户信息区域
            self.profile_frame = ctk.CTkFrame(
                self.left_frame,
                fg_color="#E8E8E8",
                corner_radius=10
            )
            self.profile_frame.pack(fill="x", pady=10)
            
            self.user_id_label = ctk.CTkLabel(
                self.profile_frame,
                text="用户 ID: -",
                font=(self.system_font, 12),
                text_color="#333333",
                anchor="w"
            )
            self.user_id_label.pack(pady=(10, 5), fill="x", padx=10)
            
            self.account_type_label = ctk.CTkLabel(
                self.profile_frame,
                text="账户类型: -",
                font=(self.system_font, 12),
                text_color="#333333",
                anchor="w"
            )
            self.account_type_label.pack(pady=5, fill="x", padx=10)
            
            self.days_left_label = ctk.CTkLabel(
                self.profile_frame,
                text="剩余天数: -",
                font=(self.system_font, 12),
                text_color="#333333",
                anchor="w"
            )
            self.days_left_label.pack(pady=(5, 10), fill="x", padx=10)
            
            # 使用量信息区域
            self.usage_frame = ctk.CTkFrame(
                self.left_frame,
                fg_color="#E8E8E8",
                corner_radius=10
            )
            self.usage_frame.pack(fill="x", pady=10)
            
            self.premium_label = ctk.CTkLabel(
                self.usage_frame,
                text="Premium 使用情况: -/-",
                font=(self.system_font, 12),
                text_color="#333333",
                anchor="w"
            )
            self.premium_label.pack(pady=(10, 5), fill="x", padx=10)
            
            self.basic_label = ctk.CTkLabel(
                self.usage_frame,
                text="Basic 使用情况: -/-",
                font=(self.system_font, 12),
                text_color="#333333",
                anchor="w"
            )
            self.basic_label.pack(pady=(5, 10), fill="x", padx=10)
            
            # 按钮区域
            self.button_frame = ctk.CTkFrame(
                self.left_frame,
                fg_color="transparent"
            )
            self.button_frame.pack(fill="x", pady=10)
            
            # 访问网站按钮
            self.visit_website_btn = ctk.CTkButton(
                self.button_frame,
                text="获取授权码",
                command=self.open_website,
                font=(self.system_font, 12),
                fg_color="#0078d4",
                hover_color="#106ebe",
                corner_radius=4,
                height=32
            )
            self.visit_website_btn.pack(fill="x", pady=(0, 5))
            
            # 更新 Token 按钮
            self.update_token_btn = ctk.CTkButton(
                self.button_frame,
                text="更新 Token",
                command=self.update_token,
                font=(self.system_font, 12),
                fg_color="#0078d4",
                hover_color="#106ebe",
                corner_radius=4,
                height=32
            )
            self.update_token_btn.pack(fill="x", pady=5)
            
            # 刷新使用量按钮
            self.refresh_btn = ctk.CTkButton(
                self.button_frame,
                text="刷新使用量",
                command=self.refresh_usage,
                font=(self.system_font, 12),
                fg_color="#2B7DE9",
                hover_color="#1C54B2",
                height=32
            )
            self.refresh_btn.pack(fill="x", pady=5)
            
            # 日志区域标题
            self.log_title = ctk.CTkLabel(
                self.right_frame,
                text="运行日志",
                font=(self.system_font, 14, "bold"),
                text_color="#333333"
            )
            self.log_title.pack(pady=(0, 10))
            
            # 日志显示区域
            self.log_frame = ctk.CTkFrame(
                self.right_frame,
                fg_color="#E8E8E8",
                corner_radius=10
            )
            self.log_frame.pack(fill="both", expand=True)
            
            # 日志文本框
            self.log_text = ctk.CTkTextbox(
                self.log_frame,
                font=(self.system_font, 12),
                text_color="#333333",
                wrap="word"
            )
            self.log_text.pack(fill="both", expand=True, padx=5, pady=5)
            
            # 设置日志处理
            self.log_queue = queue.Queue()
            self.queue_handler = QueueHandler(self.log_queue)
            self.queue_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
            
            # 添加队列处理器前先检查是否已存在
            if not any(isinstance(h, QueueHandler) for h in logger.handlers):
                logger.addHandler(self.queue_handler)
            
            # 启动日志更新
            self.update_log()
            
        except Exception as e:
            error_msg = traceback.format_exc()
            logger.error(f"GUI初始化失败:\n{error_msg}")
            raise

    def update_log(self):
        """更新日志显示"""
        while True:
            try:
                log_message = self.log_queue.get_nowait()
                self.log_text.insert("end", log_message + "\n")
                self.log_text.see("end")
            except queue.Empty:
                break
        self.root.after(100, self.update_log)

    def open_website(self):
        """打开官网"""
        webbrowser.open("https://cursor.ccopilot.org/")

    def update_usage_display(self, usage: Optional[Dict]):
        """更新使用量显示"""
        if usage:
            self.premium_label.configure(
                text=f'Premium 使用情况: {usage["premium_usage"]}/{usage["max_premium_usage"]}'
            )
            self.basic_label.configure(
                text=f'Basic 使用情况: {usage["basic_usage"]}/{usage["max_basic_usage"]}'
            )
        else:
            self.premium_label.configure(text="Premium 使用情况: 获取失败")
            self.basic_label.configure(text="Basic 使用情况: 获取失败")

    def update_profile_display(self, profile: Optional[Dict], user_id: str):
        """更新订阅信息显示"""
        if profile:
            self.user_id_label.configure(text=f"用户 ID: {user_id}")
            self.account_type_label.configure(text=f"账户类型: {profile['membershipType']}")
            self.days_left_label.configure(text=f"剩余天数: {profile['daysRemainingOnTrial']}")
        else:
            self.user_id_label.configure(text="用户 ID: 获取失败")
            self.account_type_label.configure(text="账户类型: 获取失败")
            self.days_left_label.configure(text="剩余天数: 获取失败")

    def refresh_usage(self):
        """刷新使用量信息"""
        def refresh_task():
            self.refresh_btn.configure(state="disabled")
            
            auth_manager = CursorAuthManager()
            token = auth_manager.get_auth_token()
            
            if not token:
                logger.error("未找到有效的token")
                self.refresh_btn.configure(state="normal")
                return
                
            # 获取并显示订阅信息
            profile = UsageManager.get_stripe_profile(token)
            user_id = UsageManager.extract_user_id_from_jwt(token)
            self.update_profile_display(profile, user_id)
            
            # 获取并显示使用量
            usage = UsageManager.get_usage(token)
            self.update_usage_display(usage)
            
            self.refresh_btn.configure(state="normal")
            
        threading.Thread(target=refresh_task, daemon=True).start()

    def show_input_dialog(self):
        """显示输入对话框"""
        # 创建新窗口
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("输入授权码")
        dialog.geometry("500x200")
        dialog.transient(self.root)
        
        # 居中显示
        self.center_window(dialog)
        
        # 创建一个框架来容纳所有组件
        content_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 提示文本
        hint_label = ctk.CTkLabel(
            content_frame,
            text="请输入授权码（获取地址：https://cursor.ccopilot.org）:",
            font=(self.system_font, 12)
        )
        hint_label.pack(pady=(0, 10))
        
        # 授权码输入框
        code_entry = ctk.CTkEntry(content_frame, width=460)
        code_entry.pack(pady=10)
        
        result = {"code": None}
        
        def on_submit():
            # 使用 strip() 去除两端空格
            result["code"] = code_entry.get().strip()
            dialog.destroy()
            
        def on_cancel():
            result["code"] = None
            dialog.destroy()
        
        # 按钮框架
        btn_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        btn_frame.pack(fill="x", pady=20)
        
        # 确认按钮
        submit_btn = ctk.CTkButton(
            btn_frame,
            text="确认",
            command=on_submit,
            width=100
        )
        submit_btn.pack(side="left", padx=10)
        
        # 取消按钮
        cancel_btn = ctk.CTkButton(
            btn_frame,
            text="取消",
            command=on_cancel,
            width=100
        )
        cancel_btn.pack(side="right", padx=10)
        
        # 等待窗口关闭
        self.root.wait_window(dialog)
        return result["code"]

    def show_manual_input_dialog(self):
        """显示手动输入对话框"""
        # 创建新窗口
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("手动输入Token信息")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        
        # 居中显示
        self.center_window(dialog)
        
        # 创建一个框架来容纳所有组件
        content_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 邮箱输入框
        email_label = ctk.CTkLabel(content_frame, text="邮箱:", anchor="w")
        email_label.pack(fill="x", pady=(0,5))
        email_entry = ctk.CTkEntry(content_frame, width=360)
        email_entry.pack()
        
        # Token输入框
        token_label = ctk.CTkLabel(content_frame, text="Token:", anchor="w")
        token_label.pack(fill="x", pady=(20,5))
        token_entry = ctk.CTkEntry(content_frame, width=360)
        token_entry.pack()
        
        result = {"email": None, "token": None}
        
        def on_submit():
            # 使用 strip() 去除两端空格
            result["email"] = email_entry.get().strip()
            result["token"] = token_entry.get().strip()
            dialog.destroy()
            
        # 确认按钮
        submit_btn = ctk.CTkButton(content_frame, text="确认", command=on_submit)
        submit_btn.pack(pady=30)
        
        # 等待窗口关闭
        self.root.wait_window(dialog)
        return result if result["email"] and result["token"] else None

    def show_token_method_dialog(self):
        """显示选择Token获取方式的对话框"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("选择Token获取方式")
        dialog.geometry("300x200")
        dialog.transient(self.root)
        
        # 居中显示
        self.center_window(dialog)
        
        result = {"choice": None}
        
        def on_choice(choice):
            result["choice"] = choice
            dialog.destroy()
            
        # 选项按钮
        ctk.CTkButton(
            dialog,
            text="从API自动获取",
            command=lambda: on_choice(1)
        ).pack(pady=(20,10), padx=20, fill="x")
        
        ctk.CTkButton(
            dialog,
            text="手动输入email和token",
            command=lambda: on_choice(2)
        ).pack(pady=10, padx=20, fill="x")
        
        ctk.CTkButton(
            dialog,
            text="取消",
            command=lambda: on_choice(3)
        ).pack(pady=10, padx=20, fill="x")
        
        # 等待窗口关闭
        self.root.wait_window(dialog)
        return result["choice"]

    def get_token_data(self, cursor_version: str):
        """获取Token数据的GUI实现"""
        choice = self.show_token_method_dialog()
        
        if choice == 3 or choice is None:  # 取消或关闭窗口
            logger.info("已取消操作")
            return None
            
        if choice == 2:  # 手动输入
            result = self.show_manual_input_dialog()
            if not result:
                logger.error("未输入完整信息")
                return None
                
            ids = generate_ids()
            return TokenData(
                mac_machine_id=ids['mac_machine_id'],
                machine_id=ids['machine_id'],
                dev_device_id=ids['dev_device_id'],
                email=result["email"],
                token=result["token"]
            )
        else:  # API获取
            access_code = self.show_input_dialog()
            if not access_code:
                logger.error("未输入授权码")
                return None
            return TokenManager.fetch_token_data(access_code, cursor_version)

    def show_disable_update_dialog(self):
        """显示禁用更新确认对话框"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("禁用自动更新")
        dialog.geometry("350x150") 
        dialog.transient(self.root)
        
        # 居中显示
        self.center_window(dialog)
        
        # 提示文本
        label = ctk.CTkLabel(
            dialog,
            text="建议禁用 Cursor 自动更新以避免更新后失效\n是否禁用自动更新？",
            font=(self.system_font, 12)
        )
        label.pack(pady=20)
        
        # 按钮框架
        btn_frame = ctk.CTkFrame(dialog, fg_color="transparent")
        btn_frame.pack(fill="x", padx=20)
        
        result = {"choice": False}
        
        def on_yes():
            result["choice"] = True
            dialog.destroy()
            
        def on_no():
            result["choice"] = False
            dialog.destroy()
        
        # 是/否按钮
        yes_btn = ctk.CTkButton(
            btn_frame,
            text="是",
            command=on_yes,
            width=80  # 减小按钮宽度
        )
        yes_btn.pack(side="left", padx=5)  # 减小按钮间距
        
        no_btn = ctk.CTkButton(
            btn_frame,
            text="否", 
            command=on_no,
            width=80  # 减小按钮宽度
        )
        no_btn.pack(side="right", padx=5)  # 减小按钮间距
        
        # 等待对话框关闭
        dialog.wait_window()
        return result["choice"]

    def handle_update_disable(self):
        """处理禁用自动更新"""
        # 检查是否需要继续禁用更新流程
        if not UpdateManager.disable_auto_update_main():
            return False
        
        # 显示确认对话框
        if self.show_disable_update_dialog():
            # 用户确认禁用,执行禁用操作
            return UpdateManager.disable_auto_update()
        else:
            # 用户取消禁用时的提示
            logger.info("已取消执行禁用自动更新，如果Cursor更新后脚本失效，需要重新执行脚本")
            return False

    def update_token(self):
        """更新Token"""
        def update_task():
            logger.info("准备更新Token...")
            self.update_token_btn.configure(state="disabled")
            
            # 获取Cursor路径
            pkg_path, main_path = FilePathManager.get_cursor_app_paths()
            
            if not Utils.check_files_exist(pkg_path, main_path):
                logger.error("请检查是否正确安装 Cursor")
                self.update_token_btn.configure(state="normal")
                return
                
            # 检查版本
            try:
                import json
                cursor_version = json.loads(pkg_path.read_text(encoding="utf-8"))["version"]
                logger.info(f"当前 Cursor 版本: {cursor_version}")
                need_patch = CursorPatcher.check_version(cursor_version)
                if not need_patch:
                    logger.info("当前版本无需 Patch，继续执行 Token 更新...")
            except Exception as e:
                logger.error(f"读取版本信息失败: {str(e)}")
                self.update_token_btn.configure(state="normal")
                return
                
            token_data = self.get_token_data(cursor_version)
            if not token_data:
                logger.error("获取Token数据失败")
                self.update_token_btn.configure(state="normal")
                return
                
            logger.info("正在退出 Cursor...")
            if not CursorManager.exit_cursor():
                logger.error("退出 Cursor 失败")
                self.update_token_btn.configure(state="normal")
                return
                
            if need_patch:
                logger.info("正在应用补丁...")
                if not CursorPatcher.patch_main_js(main_path):
                    logger.error("Patch 失败")
                    self.update_token_btn.configure(state="normal")
                    return
                    
            logger.info("正在更新Token...")
            if not TokenManager.update_token(token_data):
                logger.error("更新Token失败")
                self.update_token_btn.configure(state="normal")
                return
                
            logger.info("Token更新成功!")

            # 处理禁用更新
            logger.info("正在检查是否需要禁用自动更新...")
            logger.info("请注意：建议禁用 Cursor 自动更新!!!")
            logger.info("从 0.45.xx 开始每次更新都需要重新执行此脚本")
            
            if self.handle_update_disable():
                logger.info("已成功禁用自动更新")
            
            self.update_token_btn.configure(state="normal")
            
            # 更新成功后自动刷新使用量
            self.refresh_usage()
            
            # 添加完成提示
            logger.info("所有操作已完成，现在可以重新打开Cursor体验了\n\n")

        threading.Thread(target=update_task, daemon=True).start()

    def run(self):
        """运行GUI程序"""
        # 使用 after 方法延迟执行刷新，让界面先完成渲染
        self.root.after(1000, self.refresh_usage)  # 延迟1秒后刷新
        self.root.mainloop()

    def center_window(self, window):
        """将窗口居中显示"""
        # 获取屏幕尺寸
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        # 对于主窗口，使用预设的尺寸
        if window == self.root:
            width = 800
            height = 500
        else:
            # 对于其他窗口（如对话框），使用其请求的尺寸
            window.update_idletasks()
            width = window.winfo_width()
            height = window.winfo_height()
        
        # 计算位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        # 设置窗口位置
        window.geometry(f'{width}x{height}+{x}+{y}')

    def get_system_font(self):
        """获取系统字体,带回退机制"""
        system_fonts = [
            "Microsoft YaHei UI",
            "Microsoft YaHei", 
            "SimHei",
            "Arial",
            "Segoe UI"
        ]
        
        # 检查系统是否有这些字体
        for font in system_fonts:
            try:
                test_label = ctk.CTkLabel(self.root, text="Test", font=(font, 12))
                test_label.destroy()
                return font
            except:
                continue
            
        return "TkDefaultFont"  # 最后回退到 Tk 默认字体

def main():
    try:
        app = CursorGUI()
        logger.info("GUI初始化完成")
        app.run()
        
    except Exception as e:
        error_msg = traceback.format_exc()
        logger.error(f"程序启动失败:\n{error_msg}")
        if hasattr(sys, 'frozen'):
            input("\n程序发生错误，按回车键退出...")

if __name__ == "__main__":
    main() 